# Concierge Agent Workflow Template
# Optimized for greeting, guidance, and persona recommendation workflows

template_id: "concierge_workflow"
template_name: "Concierge Agent Workflow"
version: "1.0.0"
description: "Optimized workflow template for concierge agent interactions with minimal routing complexity"
agent_type: "concierge"

# Template metadata
metadata:
  author: "Datagenius Team"
  created_date: "2024-01-26"
  last_modified: "2024-01-26"
  tags: ["concierge", "guidance", "recommendation", "simple"]
  complexity: "low"
  estimated_duration: "5-15 seconds"

# Workflow configuration
workflow_config:
  # Execution settings
  max_execution_steps: 3  # Keep concierge workflows short
  execution_timeout: 30   # 30 seconds max
  enable_parallel_execution: false  # Sequential for simplicity
  
  # Loop prevention
  max_agent_executions: 2  # Very limited for concierge
  min_execution_interval: 1.0  # 1 second minimum between executions
  enable_infinite_loop_detection: true
  
  # Termination conditions
  auto_terminate_on_response: true  # Terminate after first response
  require_explicit_continuation: false
  enable_workflow_completion_signals: true

# Node configuration
nodes:
  # Entry point - always start with concierge
  entry:
    type: "agent"
    agent_id: "concierge"
    config:
      max_retries: 1
      timeout: 15
      enable_fallback: true
    
  # Routing node (minimal usage)
  routing:
    type: "routing"
    config:
      enable_intelligent_routing: false  # Disable for simplicity
      fallback_to_concierge: true
      max_routing_attempts: 1
    
  # Tool execution (if needed)
  tools:
    type: "tool_execution"
    config:
      max_tool_calls: 2
      tool_timeout: 10
      enable_parallel_tools: false

# Edge configuration (routing patterns)
edges:
  # Start -> Concierge (direct entry)
  - from: "START"
    to: "concierge"
    condition: "always"
    priority: 1
    
  # Concierge -> END (primary path)
  - from: "concierge"
    to: "END"
    condition: "workflow_complete OR response_generated"
    priority: 1
    
  # Concierge -> Tools (if tools needed)
  - from: "concierge"
    to: "tools"
    condition: "tools_required AND NOT workflow_complete"
    priority: 2
    
  # Tools -> Concierge (return after tools)
  - from: "tools"
    to: "concierge"
    condition: "tools_completed"
    priority: 1
    
  # Emergency fallback to END
  - from: "concierge"
    to: "END"
    condition: "error OR timeout OR max_executions_reached"
    priority: 3

# Termination conditions
termination_conditions:
  # Primary termination - response generated
  - condition: "response_generated"
    action: "END"
    priority: 1
    
  # Workflow completion signal
  - condition: "workflow_complete"
    action: "END"
    priority: 1
    
  # Error conditions
  - condition: "agent_error OR processing_error"
    action: "END"
    priority: 2
    
  # Timeout conditions
  - condition: "execution_timeout OR agent_timeout"
    action: "END"
    priority: 2
    
  # Loop prevention
  - condition: "infinite_loop_detected OR max_executions_reached"
    action: "END"
    priority: 3

# State management
state_management:
  # Required state fields
  required_fields:
    - "user_id"
    - "conversation_id"
    - "messages"
    
  # State validation rules
  validation_rules:
    - field: "messages"
      rule: "not_empty"
      error_action: "fallback_response"
      
    - field: "user_id"
      rule: "not_null"
      error_action: "continue_with_default"
  
  # State cleanup
  cleanup_on_completion: true
  preserve_conversation_history: true

# Performance optimization
performance:
  # Caching
  enable_response_caching: true
  cache_duration: 300  # 5 minutes
  
  # Resource limits
  memory_limit_mb: 50
  cpu_time_limit_seconds: 10
  
  # Monitoring
  enable_performance_tracking: true
  track_response_times: true
  track_resource_usage: true

# Error handling
error_handling:
  # Retry configuration
  max_retries: 1
  retry_delay_seconds: 0.5
  exponential_backoff: false
  
  # Fallback strategies
  fallback_strategies:
    - trigger: "agent_unavailable"
      action: "use_default_response"
      
    - trigger: "processing_error"
      action: "return_error_message"
      
    - trigger: "timeout"
      action: "return_timeout_message"
  
  # Error responses
  default_error_response: "I apologize, but I'm having trouble processing your request right now. Please try again or rephrase your question."
  timeout_response: "I'm taking longer than expected to respond. Please try again with a simpler request."

# Integration settings
integrations:
  # Business profile integration
  business_profile:
    enable: true
    required: false
    fallback_on_missing: true
    
  # Cross-agent intelligence
  cross_agent_intelligence:
    enable: false  # Disabled for concierge simplicity
    
  # MCP tools
  mcp_tools:
    enable: true
    max_tools: 2
    timeout: 10

# Monitoring and metrics
monitoring:
  # Metrics to collect
  metrics:
    - "response_time"
    - "success_rate"
    - "error_rate"
    - "user_satisfaction"
    - "workflow_completion_rate"
    
  # Alerts
  alerts:
    - metric: "error_rate"
      threshold: 0.1  # 10%
      action: "log_warning"
      
    - metric: "response_time"
      threshold: 10.0  # 10 seconds
      action: "log_performance_issue"

# Template validation
validation:
  # Required components
  required_components:
    - "concierge_agent"
    - "routing_node"
    
  # Optional components
  optional_components:
    - "tool_execution_node"
    - "business_context_manager"
    
  # Validation rules
  rules:
    - rule: "max_execution_steps <= 5"
      error: "Concierge workflows should be simple and fast"
      
    - rule: "execution_timeout <= 60"
      error: "Concierge workflows should complete quickly"
