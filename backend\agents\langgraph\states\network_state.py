"""
Network State Schema for LangGraph Network of Agents.

This module extends the unified state to support network-style communication
where agents can directly communicate with each other and coordinate workflows.
"""

import logging
from typing import TypedDict, Annotated, List, Dict, Any, Optional, Set
from datetime import datetime
from enum import Enum
import operator
import uuid

from .unified_state import UnifiedDatageniusState, MessageType, AgentRole

logger = logging.getLogger(__name__)


class NetworkCommunicationType(str, Enum):
    """Types of network communication between agents."""
    DIRECT_MESSAGE = "direct_message"
    CONSULTATION_REQUEST = "consultation_request"
    CONSULTATION_RESPONSE = "consultation_response"
    DELEGATION = "delegation"
    COLLABORATION_INVITE = "collaboration_invite"
    CONSENSUS_VOTE = "consensus_vote"
    INSIGHT_SHARE = "insight_share"
    HANDOFF = "handoff"
    STATUS_UPDATE = "status_update"


class NetworkMessagePriority(str, Enum):
    """Priority levels for network messages."""
    CRITICAL = "critical"
    HIGH = "high"
    NORMAL = "normal"
    LOW = "low"


class AgentNetworkRole(str, Enum):
    """Roles agents can have in network communications."""
    INITIATOR = "initiator"
    RESPONDER = "responder"
    COORDINATOR = "coordinator"
    PARTICIPANT = "participant"
    OBSERVER = "observer"
    SPECIALIST = "specialist"


class NetworkDatageniusState(UnifiedDatageniusState):
    """
    Enhanced state schema for network-based multi-agent communication.
    
    Extends UnifiedDatageniusState with network-specific capabilities:
    - Direct agent-to-agent messaging
    - Network topology tracking
    - Distributed workflow coordination
    - Multi-agent consensus mechanisms
    - Dynamic team formation
    """
    
    # === NETWORK COMMUNICATION ===
    network_messages: Annotated[List[Dict[str, Any]], operator.add]
    pending_communications: Dict[str, List[Dict[str, Any]]]
    communication_history: Annotated[List[Dict[str, Any]], operator.add]
    
    # === NETWORK TOPOLOGY ===
    network_agents: Dict[str, Dict[str, Any]]  # Available agents and their capabilities
    agent_connections: Dict[str, Set[str]]  # Direct connections between agents
    network_topology: Dict[str, Any]  # Current network structure
    
    # === DISTRIBUTED COORDINATION ===
    active_workflows: Dict[str, Dict[str, Any]]  # Multi-agent workflows in progress
    workflow_participants: Dict[str, List[str]]  # Agents participating in each workflow
    coordination_state: Dict[str, Any]  # Current coordination status
    
    # === CONSULTATION & DELEGATION ===
    consultation_requests: List[Dict[str, Any]]
    delegation_tasks: Dict[str, Dict[str, Any]]
    specialist_assignments: Dict[str, str]  # Task -> Specialist mapping
    
    # === CONSENSUS & VOTING ===
    active_votes: Dict[str, Dict[str, Any]]
    consensus_requirements: Dict[str, Dict[str, Any]]
    voting_results: Dict[str, Any]
    
    # === DYNAMIC TEAM FORMATION ===
    active_teams: Dict[str, Dict[str, Any]]
    team_formation_requests: List[Dict[str, Any]]
    team_performance_metrics: Dict[str, Dict[str, float]]
    
    # === NETWORK INTELLIGENCE ===
    network_insights: Annotated[List[Dict[str, Any]], operator.add]
    capability_requests: List[Dict[str, Any]]
    network_learning_data: Dict[str, Any]
    
    # === PERFORMANCE & MONITORING ===
    network_performance_metrics: Dict[str, Dict[str, float]]
    communication_latency: Dict[str, float]
    agent_availability: Dict[str, bool]
    network_health_status: Dict[str, Any]


def create_network_state(
    user_id: str,
    conversation_id: str,
    workflow_type: str = "network",
    initial_message: Optional[Dict[str, Any]] = None,
    business_profile_id: Optional[str] = None,
    available_agents: Optional[List[Dict[str, Any]]] = None,
    **kwargs
) -> NetworkDatageniusState:
    """
    Create a new network state for multi-agent workflows.
    
    Args:
        user_id: User identifier
        conversation_id: Conversation identifier
        workflow_type: Type of workflow being created
        initial_message: Optional initial message
        business_profile_id: Optional business profile context
        available_agents: List of available agents in the network
        **kwargs: Additional state fields
        
    Returns:
        Initialized NetworkDatageniusState
    """
    from .unified_state import create_unified_state
    
    # Create base unified state
    base_state = create_unified_state(
        user_id=user_id,
        conversation_id=conversation_id,
        workflow_type=workflow_type,
        initial_message=initial_message,
        business_profile_id=business_profile_id,
        **kwargs
    )
    
    # Add network-specific fields
    network_state = NetworkDatageniusState(**base_state)
    
    # Initialize network-specific state
    network_state.update({
        # Network communication
        "network_messages": [],
        "pending_communications": {},
        "communication_history": [],
        
        # Network topology
        "network_agents": {agent["agent_id"]: agent for agent in (available_agents or [])},
        "agent_connections": {},
        "network_topology": {"nodes": [], "edges": []},
        
        # Distributed coordination
        "active_workflows": {},
        "workflow_participants": {},
        "coordination_state": {"status": "initialized", "coordinator": None},
        
        # Consultation & delegation
        "consultation_requests": [],
        "delegation_tasks": {},
        "specialist_assignments": {},
        
        # Consensus & voting
        "active_votes": {},
        "consensus_requirements": {},
        "voting_results": {},
        
        # Dynamic team formation
        "active_teams": {},
        "team_formation_requests": [],
        "team_performance_metrics": {},
        
        # Network intelligence
        "network_insights": [],
        "capability_requests": [],
        "network_learning_data": {},
        
        # Performance & monitoring
        "network_performance_metrics": {},
        "communication_latency": {},
        "agent_availability": {},
        "network_health_status": {"status": "healthy", "last_check": datetime.now().isoformat()}
    })
    
    return network_state


def add_network_message(
    state: NetworkDatageniusState,
    sender_agent: str,
    recipient_agent: str,
    message_type: NetworkCommunicationType,
    content: Dict[str, Any],
    priority: NetworkMessagePriority = NetworkMessagePriority.NORMAL,
    metadata: Optional[Dict[str, Any]] = None
) -> NetworkDatageniusState:
    """
    Add a network message between agents.
    
    Args:
        state: Current network state
        sender_agent: ID of the sending agent
        recipient_agent: ID of the receiving agent
        message_type: Type of network communication
        content: Message content
        priority: Message priority level
        metadata: Additional metadata
        
    Returns:
        Updated state with new network message
    """
    message = {
        "id": str(uuid.uuid4()),
        "sender_agent": sender_agent,
        "recipient_agent": recipient_agent,
        "message_type": message_type.value,
        "content": content,
        "priority": priority.value,
        "timestamp": datetime.now().isoformat(),
        "metadata": metadata or {},
        "status": "sent"
    }
    
    # Add to network messages
    state["network_messages"].append(message)
    
    # Add to pending communications for recipient
    if recipient_agent not in state["pending_communications"]:
        state["pending_communications"][recipient_agent] = []
    state["pending_communications"][recipient_agent].append(message)
    
    # Update communication history
    state["communication_history"].append({
        "message_id": message["id"],
        "sender": sender_agent,
        "recipient": recipient_agent,
        "type": message_type.value,
        "timestamp": message["timestamp"]
    })
    
    # Establish connection if not exists
    if sender_agent not in state["agent_connections"]:
        state["agent_connections"][sender_agent] = set()
    if recipient_agent not in state["agent_connections"]:
        state["agent_connections"][recipient_agent] = set()
    
    state["agent_connections"][sender_agent].add(recipient_agent)
    state["agent_connections"][recipient_agent].add(sender_agent)
    
    state["updated_at"] = datetime.now().isoformat()
    return state


def create_consultation_request(
    state: NetworkDatageniusState,
    requesting_agent: str,
    specialist_agent: str,
    consultation_topic: str,
    context: Dict[str, Any],
    urgency: str = "normal"
) -> NetworkDatageniusState:
    """
    Create a consultation request between agents.
    
    Args:
        state: Current network state
        requesting_agent: Agent requesting consultation
        specialist_agent: Specialist agent being consulted
        consultation_topic: Topic of consultation
        context: Context information for the consultation
        urgency: Urgency level of the request
        
    Returns:
        Updated state with consultation request
    """
    consultation_id = str(uuid.uuid4())
    
    consultation_request = {
        "id": consultation_id,
        "requesting_agent": requesting_agent,
        "specialist_agent": specialist_agent,
        "topic": consultation_topic,
        "context": context,
        "urgency": urgency,
        "status": "pending",
        "created_at": datetime.now().isoformat(),
        "response": None
    }
    
    # Add to consultation requests
    state["consultation_requests"].append(consultation_request)
    
    # Send network message
    state = add_network_message(
        state=state,
        sender_agent=requesting_agent,
        recipient_agent=specialist_agent,
        message_type=NetworkCommunicationType.CONSULTATION_REQUEST,
        content={
            "consultation_id": consultation_id,
            "topic": consultation_topic,
            "context": context,
            "urgency": urgency
        },
        priority=NetworkMessagePriority.HIGH if urgency == "high" else NetworkMessagePriority.NORMAL
    )
    
    return state


def create_team_formation_request(
    state: NetworkDatageniusState,
    initiating_agent: str,
    required_capabilities: List[str],
    team_size: int,
    task_description: str,
    duration_estimate: Optional[int] = None
) -> NetworkDatageniusState:
    """
    Create a request for dynamic team formation.
    
    Args:
        state: Current network state
        initiating_agent: Agent initiating team formation
        required_capabilities: List of required capabilities
        team_size: Desired team size
        task_description: Description of the task requiring a team
        duration_estimate: Estimated duration in minutes
        
    Returns:
        Updated state with team formation request
    """
    team_request = {
        "id": str(uuid.uuid4()),
        "initiating_agent": initiating_agent,
        "required_capabilities": required_capabilities,
        "team_size": team_size,
        "task_description": task_description,
        "duration_estimate": duration_estimate,
        "status": "pending",
        "created_at": datetime.now().isoformat(),
        "candidate_agents": [],
        "formed_team": None
    }
    
    state["team_formation_requests"].append(team_request)
    state["updated_at"] = datetime.now().isoformat()
    
    return state


def update_network_topology(
    state: NetworkDatageniusState,
    agents: List[Dict[str, Any]],
    connections: Dict[str, List[str]]
) -> NetworkDatageniusState:
    """
    Update the network topology with current agents and connections.
    
    Args:
        state: Current network state
        agents: List of agent information
        connections: Dictionary of agent connections
        
    Returns:
        Updated state with new topology
    """
    # Update network agents
    for agent in agents:
        state["network_agents"][agent["agent_id"]] = agent
    
    # Update connections
    for agent_id, connected_agents in connections.items():
        state["agent_connections"][agent_id] = set(connected_agents)
    
    # Update topology representation
    nodes = [{"id": agent_id, "data": agent_data} for agent_id, agent_data in state["network_agents"].items()]
    edges = []
    
    for agent_id, connections in state["agent_connections"].items():
        for connected_agent in connections:
            if agent_id < connected_agent:  # Avoid duplicate edges
                edges.append({"source": agent_id, "target": connected_agent})
    
    state["network_topology"] = {"nodes": nodes, "edges": edges}
    state["updated_at"] = datetime.now().isoformat()
    
    return state
