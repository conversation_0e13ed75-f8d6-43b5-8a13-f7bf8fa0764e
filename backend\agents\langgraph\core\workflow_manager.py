"""
LangGraph Workflow Manager for Datagenius.

This module provides the main workflow manager that replaces the current
orchestrator system. It handles workflow creation, execution, and management
using LangGraph's graph-based approach.
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List, Type, Callable, AsyncGenerator
from datetime import datetime
import uuid

from langgraph.graph import State<PERSON>raph, END, START
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import ToolNode
import copy

from ..states.unified_state import (
    UnifiedDatageniusState,
    create_unified_state,
    update_agent_transition,
    add_message,
    update_workflow_status,
    WorkflowStatus,
    MessageType
)
from .routing_constants import RoutingConstants, ValidationError, AgentNotFoundError
from .routing_utils import create_routing_decision_maker, RoutingDecisionMaker
from .workflow_template_selector import template_selector
from ..nodes.base_agent_node import BaseAgentNode
from ..nodes.routing_node import AgentSwitchNode, RoutingNode  # Import both for backward compatibility
from ..nodes.tool_execution_node import ToolExecutionNode
from ..persistence.unified_checkpointer import <PERSON><PERSON>he<PERSON>pointer
from ..integrations.phase3_integration_service import Phase3IntegrationService
from ..monitoring.workflow_monitor import WorkflowMonitor

# Import configuration with robust path handling
import sys
import importlib.util
from pathlib import Path

# Get backend root directory
backend_root = Path(__file__).parent.parent.parent.parent

# Function to import config module dynamically
def _import_phase3_config():
    """Dynamically import phase3_config module."""
    config_path = backend_root / "config" / "phase3_config.py"

    if config_path.exists():
        spec = importlib.util.spec_from_file_location("phase3_config", config_path)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        return module
    else:
        raise ImportError(f"Could not find phase3_config.py at {config_path}")

# Import the functions
try:
    phase3_config_module = _import_phase3_config()
    get_phase3_config = phase3_config_module.get_phase3_config
    is_phase3_enabled = phase3_config_module.is_phase3_enabled
except Exception as e:
    # Fallback: create dummy functions to prevent import errors
    def get_phase3_config():
        from dataclasses import dataclass
        from enum import Enum

        class OptimizationLevel(Enum):
            STANDARD = "standard"

        @dataclass
        class Phase3Config:
            enable_phase3: bool = True
            optimization_level: OptimizationLevel = OptimizationLevel.STANDARD
            debug_mode: bool = False
            enable_predictive_optimization: bool = True
            enable_self_healing: bool = True
            enable_advanced_collaboration: bool = True

        return Phase3Config()

    def is_phase3_enabled():
        return True

logger = logging.getLogger(__name__)


class WorkflowManager:
    """
    Main workflow manager for the unified persona system.

    This manager provides:
    - Dynamic workflow graph construction
    - Persona coordination and routing
    - Tool integration and execution
    - State management and persistence
    - Cross-persona intelligence coordination
    - Performance monitoring and error handling
    """
    
    def __init__(self):
        """Initialize the workflow manager."""
        self.logger = logging.getLogger(__name__)

        # Core components
        self.checkpointer = UnifiedCheckpointer()
        self.memory_saver = MemorySaver()

        # Agent registry
        self.agent_nodes: Dict[str, BaseAgentNode] = {}
        self.tool_nodes: Dict[str, ToolExecutionNode] = {}

        # Routing decision maker (will be initialized after agents are loaded)
        self.routing_decision_maker: Optional[RoutingDecisionMaker] = None

        # Workflow cache
        self.workflow_cache: Dict[str, StateGraph] = {}

        # Performance metrics
        self.metrics = {
            "workflows_created": 0,
            "workflows_completed": 0,
            "workflows_failed": 0,
            "total_execution_time": 0.0
        }

        # Circuit breaker for infinite loop prevention
        self.workflow_circuit_breakers: Dict[str, Dict[str, Any]] = {}
        self.max_agent_executions_per_workflow = 10  # Reduced from 20
        self.max_same_agent_consecutive_executions = 3  # New limit

        # Phase 3: AI-powered optimization and self-healing
        self.workflow_monitor = WorkflowMonitor()
        self.phase3_enabled = is_phase3_enabled()
        self.phase3_config = get_phase3_config() if self.phase3_enabled else None
        self.phase3_service = None

        if self.phase3_enabled:
            self.phase3_service = Phase3IntegrationService(self.workflow_monitor)

        # Phase 4: Platform Evolution - Marketplace and AI Composition
        self.phase4_enabled = True  # Enable Phase 4 features by default
        self.phase4_platform = None  # Will be initialized asynchronously

        # Phase 5: Migration Completion - Final deployment and optimization
        self.phase5_enabled = True  # Enable Phase 5 features by default
        self.phase5_migration_completion = None  # Will be initialized asynchronously

        # Agent initialization will be done lazily on first use
        self._agents_initialized = False

        # Connect to dynamic agent manager
        self._setup_dynamic_agent_manager()

        # Phase services will be initialized lazily on first use
        # Removed asyncio.create_task from __init__ to prevent event loop issues
        self._phase3_initialized = False
        self._phase4_initialized = False
        self._phase5_initialized = False

        phase_status = []
        if self.phase3_enabled:
            phase_status.append("Phase 3 AI")
        phase_status.append("Phase 4 Platform")
        phase_status.append("Phase 5 Migration")

        self.logger.info(f"WorkflowManager initialized with {', '.join(phase_status)} enhancements")

    def _determine_agent_type(self, selected_agent: Optional[str]) -> str:
        """
        Determine the agent type from the selected agent ID.

        Args:
            selected_agent: Selected agent identifier

        Returns:
            Agent type (concierge, analysis, marketing, classification, etc.)
        """
        if not selected_agent:
            return "concierge"  # Default to concierge

        # Extract agent type from agent ID
        agent_id_lower = selected_agent.lower()

        if "concierge" in agent_id_lower:
            return "concierge"
        elif "analysis" in agent_id_lower or "analyst" in agent_id_lower:
            return "analysis"
        elif "marketing" in agent_id_lower or "marketer" in agent_id_lower:
            return "marketing"
        elif "classification" in agent_id_lower or "classifier" in agent_id_lower:
            return "classification"
        elif "visualization" in agent_id_lower:
            return "visualization"
        else:
            # Try to get agent type from agent node if available
            if selected_agent in self.agent_nodes:
                agent_node = self.agent_nodes[selected_agent]
                if hasattr(agent_node, 'agent_type'):
                    return agent_node.agent_type

            return "concierge"  # Fallback to concierge

    def _ensure_routing_decision_maker(self) -> None:
        """Ensure routing decision maker is initialized."""
        if not self.routing_decision_maker:
            try:
                # Ensure agents are initialized first
                if not self._agents_initialized:
                    self._initialize_agents()
                    self._agents_initialized = True

                # Create routing decision maker with available agents
                self.routing_decision_maker = create_routing_decision_maker(self.agent_nodes)
                self.logger.info("✅ Routing decision maker initialized")
            except Exception as e:
                self.logger.error(f"❌ Failed to initialize routing decision maker: {e}")
                # Create with empty agent registry as fallback
                self.routing_decision_maker = create_routing_decision_maker({})
                self.logger.warning("⚠️ Created fallback routing decision maker with empty registry")

    async def _ensure_phase3_initialized(self):
        """Ensure Phase 3 services are initialized (lazy loading)."""
        if not self._phase3_initialized and self.phase3_enabled:
            await self._initialize_phase3_services()
            self._phase3_initialized = True

    async def _ensure_phase4_initialized(self):
        """Ensure Phase 4 services are initialized (lazy loading)."""
        if not self._phase4_initialized:
            await self._initialize_phase4_services()
            self._phase4_initialized = True

    async def _ensure_phase5_initialized(self):
        """Ensure Phase 5 services are initialized (lazy loading)."""
        if not self._phase5_initialized:
            await self._initialize_phase5_services()
            self._phase5_initialized = True

    async def _ensure_agents_initialized(self):
        """Ensure agents are initialized (lazy loading)."""
        if not self._agents_initialized:
            self._initialize_agents()
            self._agents_initialized = True

    async def _ensure_agent_loaded(self, agent_id: str):
        """Ensure a specific agent is loaded (lazy loading)."""
        if agent_id not in self.agent_nodes and agent_id in getattr(self, 'available_agents', []):
            try:
                from .agent_factory import agent_factory
                agent_node = agent_factory.create_agent_node(agent_id)
                if agent_node:
                    self.agent_nodes[agent_id] = agent_node
                    self.logger.info(f"✅ Lazy-loaded agent: {agent_id}")
                    return True
                else:
                    self.logger.warning(f"❌ Failed to lazy-load agent: {agent_id}")
                    return False
            except Exception as e:
                self.logger.error(f"❌ Error lazy-loading agent {agent_id}: {e}")
                return False
        return agent_id in self.agent_nodes

    async def _initialize_phase3_services(self):
        """Initialize Phase 3 AI-powered services."""
        try:
            if self.phase3_enabled:
                self.logger.info("🤖 Initializing Phase 3 AI services...")

                # Initialize the Phase 3 integration service
                success = await self.phase3_service.initialize()
                if success:
                    await self.phase3_service.start_services()
                    self.logger.info("✅ Phase 3 AI services initialized successfully")
                else:
                    self.logger.warning("⚠️ Phase 3 AI services initialization failed, continuing without AI enhancements")
                    self.phase3_enabled = False
            else:
                self.logger.info("ℹ️ Phase 3 AI services disabled")

        except Exception as e:
            self.logger.error(f"❌ Error initializing Phase 3 services: {e}")
            self.phase3_enabled = False

    async def _initialize_phase4_services(self):
        """Initialize Phase 4 Platform Evolution services."""
        try:
            if self.phase4_enabled:
                self.logger.info("🚀 Initializing Phase 4 Platform Evolution...")

                # Import Phase 4 platform
                from ..phase4_integration import Phase4PlatformEvolution

                # Initialize the Phase 4 platform
                self.phase4_platform = Phase4PlatformEvolution()
                success = await self.phase4_platform.initialize()

                if success:
                    self.logger.info("✅ Phase 4 Platform Evolution initialized successfully")

                    # Enable marketplace-driven agent selection
                    self._enable_marketplace_agent_selection()

                    # Enable AI workflow composition
                    self._enable_ai_workflow_composition()

                else:
                    self.logger.warning("⚠️ Phase 4 Platform Evolution initialization failed, continuing without marketplace features")
                    self.phase4_enabled = False
            else:
                self.logger.info("ℹ️ Phase 4 Platform Evolution disabled")

        except Exception as e:
            self.logger.error(f"❌ Error initializing Phase 4 services: {e}")
            self.phase4_enabled = False

    async def _initialize_phase5_services(self):
        """Initialize Phase 5 Migration Completion services."""
        try:
            if self.phase5_enabled:
                self.logger.info("🏁 Initializing Phase 5 Migration Completion...")

                # Import Phase 5 migration completion
                from ..phase5_migration_completion import Phase5MigrationCompletion

                # Initialize the Phase 5 migration completion system
                self.phase5_migration_completion = Phase5MigrationCompletion()
                success = await self.phase5_migration_completion.initialize()

                if success:
                    self.logger.info("✅ Phase 5 Migration Completion initialized successfully")

                    # Enable production monitoring
                    await self._enable_production_monitoring()

                    # Enable performance optimization
                    await self._enable_performance_optimization()

                    # Complete migration if configured
                    await self._complete_migration_if_ready()

                else:
                    self.logger.warning("⚠️ Phase 5 Migration Completion initialization failed")
                    self.phase5_enabled = False
            else:
                self.logger.info("ℹ️ Phase 5 Migration Completion disabled")

        except Exception as e:
            self.logger.error(f"❌ Error initializing Phase 5 services: {e}")
            self.phase5_enabled = False

    def _enable_marketplace_agent_selection(self):
        """Enable marketplace-driven agent selection."""
        self.logger.info("🏪 Enabling marketplace-driven agent selection")
        # This would integrate with the capability marketplace for agent selection

    def _enable_ai_workflow_composition(self):
        """Enable AI workflow composition."""
        self.logger.info("🧠 Enabling AI workflow composition")
        # This would integrate with the AI workflow composer

    async def _enable_production_monitoring(self):
        """Enable production monitoring for Phase 5."""
        self.logger.info("📊 Enabling production monitoring...")

        try:
            # Import and start production monitoring
            from ..monitoring.production_monitor import start_production_monitoring
            await start_production_monitoring()

            self.logger.info("✅ Production monitoring enabled")
        except Exception as e:
            self.logger.error(f"❌ Failed to enable production monitoring: {e}")

    async def _enable_performance_optimization(self):
        """Enable performance optimization for Phase 5."""
        self.logger.info("⚡ Enabling performance optimization...")

        try:
            # Import and run performance optimization
            from ..optimization.performance_optimizer import optimize_system_performance
            optimization_results = await optimize_system_performance()

            successful_optimizations = sum(1 for result in optimization_results.values() if result.success)
            total_optimizations = len(optimization_results)

            self.logger.info(f"✅ Performance optimization completed: {successful_optimizations}/{total_optimizations} successful")
        except Exception as e:
            self.logger.error(f"❌ Failed to enable performance optimization: {e}")

    async def _complete_migration_if_ready(self):
        """Complete migration if system is ready for Phase 5."""
        self.logger.info("🚀 Checking if ready for migration completion...")

        try:
            # Check if all previous phases are properly initialized
            if self.phase3_enabled and self.phase4_enabled and self.phase5_enabled:
                self.logger.info("🎯 All phases initialized, completing migration...")

                # Complete the migration
                migration_status = await self.phase5_migration_completion.complete_migration()

                if migration_status.is_completed:
                    self.logger.info("🏆 Phase 5 migration completion successful!")
                else:
                    self.logger.warning(f"⚠️ Migration completion had issues: {migration_status.errors}")
            else:
                self.logger.info("ℹ️ Not all phases ready, skipping migration completion")

        except Exception as e:
            self.logger.error(f"❌ Failed to complete migration: {e}")

    async def compose_intelligent_workflow(self, requirements: str, context: Optional[Dict[str, Any]] = None) -> Optional[Dict[str, Any]]:
        """
        Use Phase 4 AI workflow composer to create intelligent workflows.

        Args:
            requirements: Natural language description of workflow requirements
            context: Optional business context for optimization

        Returns:
            Composed workflow information or None if Phase 4 is disabled
        """
        if not self.phase4_enabled or not self.phase4_platform:
            self.logger.warning("Phase 4 not available for intelligent workflow composition")
            return None

        try:
            self.logger.info(f"🧠 Composing intelligent workflow for: {requirements}")

            # Use Phase 4 platform to compose workflow
            result = await self.phase4_platform.compose_intelligent_workflow(requirements, context)

            self.logger.info("✅ Intelligent workflow composed successfully")
            return result

        except Exception as e:
            self.logger.error(f"❌ Error composing intelligent workflow: {e}")
            return None

    async def discover_optimal_capabilities(self, requirements: Dict[str, Any]) -> Optional[List[Dict[str, Any]]]:
        """
        Use Phase 4 marketplace to discover optimal agent capabilities.

        Args:
            requirements: Capability requirements

        Returns:
            List of optimal capabilities or None if Phase 4 is disabled
        """
        if not self.phase4_enabled or not self.phase4_platform:
            self.logger.warning("Phase 4 not available for capability discovery")
            return None

        try:
            self.logger.info("🏪 Discovering optimal capabilities from marketplace")

            # Use Phase 4 platform to discover capabilities
            capabilities = await self.phase4_platform.discover_optimal_capabilities(requirements)

            self.logger.info(f"✅ Discovered {len(capabilities)} optimal capabilities")
            return capabilities

        except Exception as e:
            self.logger.error(f"❌ Error discovering optimal capabilities: {e}")
            return None

    # Phase 5: Migration Completion Methods
    async def complete_phase5_migration(self, config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Complete Phase 5 migration with optional configuration.

        Args:
            config: Optional configuration overrides

        Returns:
            Migration completion status
        """
        if not self.phase5_enabled or not self.phase5_migration_completion:
            return {"error": "Phase 5 not enabled or not initialized"}

        try:
            if config:
                self.phase5_migration_completion.config.update(config)

            migration_status = await self.phase5_migration_completion.complete_migration()
            return {
                "success": migration_status.is_completed,
                "status": migration_status,
                "message": "Migration completed successfully" if migration_status.is_completed else "Migration had issues"
            }

        except Exception as e:
            self.logger.error(f"Failed to complete Phase 5 migration: {e}")
            return {"error": f"Migration failed: {str(e)}"}

    async def get_phase5_migration_status(self) -> Dict[str, Any]:
        """Get Phase 5 migration completion status."""
        if not self.phase5_enabled or not self.phase5_migration_completion:
            return {"error": "Phase 5 not enabled or not initialized"}

        try:
            return await self.phase5_migration_completion.get_migration_status()
        except Exception as e:
            self.logger.error(f"Failed to get Phase 5 migration status: {e}")
            return {"error": f"Failed to get status: {str(e)}"}

    async def get_system_performance_metrics(self) -> Dict[str, Any]:
        """Get current system performance metrics from Phase 5 optimization."""
        try:
            from ..optimization.performance_optimizer import get_performance_metrics
            metrics = await get_performance_metrics()
            return {
                "success": True,
                "metrics": {
                    "routing_latency_ms": metrics.routing_latency_ms,
                    "agent_response_time_ms": metrics.agent_response_time_ms,
                    "memory_usage_mb": metrics.memory_usage_mb,
                    "cpu_usage_percent": metrics.cpu_usage_percent,
                    "cache_hit_rate": metrics.cache_hit_rate,
                    "throughput_requests_per_second": metrics.throughput_requests_per_second,
                    "error_rate_percent": metrics.error_rate_percent,
                    "timestamp": metrics.timestamp.isoformat()
                }
            }
        except Exception as e:
            self.logger.error(f"Failed to get performance metrics: {e}")
            return {"error": f"Failed to get metrics: {str(e)}"}

    async def get_production_monitoring_status(self) -> Dict[str, Any]:
        """Get production monitoring status from Phase 5."""
        try:
            from ..monitoring.production_monitor import get_monitoring_status
            return await get_monitoring_status()
        except Exception as e:
            self.logger.error(f"Failed to get monitoring status: {e}")
            return {"error": f"Failed to get monitoring status: {str(e)}"}

    def _initialize_agents(self, eager_load_all: bool = False) -> None:
        """
        Initialize available agents dynamically from the agent factory.

        Args:
            eager_load_all: If True, load all agents immediately instead of lazy loading
        """
        try:
            # Import both agent factories
            from .agent_factory import agent_factory
            from .marketplace_agent_factory import MarketplaceAgentFactory

            # Initialize marketplace agent factory for persona discovery
            marketplace_factory = MarketplaceAgentFactory()

            # First, try to discover agents from persona configurations
            try:
                # Try direct persona loading without async dependencies
                self._load_personas_directly(agent_factory)

            except Exception as e:
                self.logger.error(f"Error discovering persona agents: {e}")

            # Also try async discovery if possible
            try:
                import asyncio
                # Run async discovery in sync context
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # If we're already in an async context, create a new task
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, marketplace_factory.discover_available_agents())
                        agent_definitions = future.result(timeout=30)
                else:
                    agent_definitions = asyncio.run(marketplace_factory.discover_available_agents())

                # Register discovered personas with the agent factory
                for definition in agent_definitions:
                    agent_id = definition.agent_id
                    config = definition.configuration

                    # Register the agent configuration if not already registered
                    if agent_id not in agent_factory.agent_configs:
                        agent_factory.agent_configs[agent_id] = config
                        self.logger.info(f"Registered persona agent via async: {agent_id}")

            except Exception as e:
                self.logger.debug(f"Async persona discovery failed (this is expected if dependencies are missing): {e}")

            # Get all available agent IDs dynamically from the factory
            available_agents = agent_factory.get_available_agents()

            if not available_agents:
                self.logger.warning("No agents found in factory, attempting to discover agents")
                # Try to trigger agent discovery if no agents are found
                # Use default discovery paths since we've consolidated configuration
                discovery_paths = [
                    "agents.langgraph.agents",
                    "agents.custom"
                ]

                agent_factory.discover_agents(discovery_paths)
                available_agents = agent_factory.get_available_agents()

            self.logger.info(f"Found {len(available_agents)} available agents: {list(available_agents)}")

            # Store available agents for reference
            self.available_agents = list(available_agents)

            # Determine which agents to load immediately
            if eager_load_all:
                # Load all agents immediately for workflow stability
                agents_to_load = available_agents
                self.logger.info("🚀 Eager loading ALL agents for workflow stability")
            else:
                # Only initialize critical agents immediately (concierge for fallback)
                agents_to_load = [agent_id for agent_id in available_agents if 'concierge' in agent_id.lower()]
                self.logger.info(f"🎯 Loading critical agents only: {agents_to_load}")

            # Load the determined agents
            loaded_count = 0
            for agent_id in agents_to_load:
                try:
                    agent_node = agent_factory.create_agent_node(agent_id)
                    if agent_node:
                        self.agent_nodes[agent_id] = agent_node
                        loaded_count += 1
                        self.logger.info(f"✅ Loaded agent: {agent_id}")
                    else:
                        self.logger.warning(f"❌ Failed to create agent node: {agent_id}")
                except Exception as e:
                    self.logger.error(f"❌ Error loading agent {agent_id}: {e}")

            if eager_load_all:
                self.logger.info(f"🎯 Eager-loaded {loaded_count}/{len(available_agents)} agents")
            else:
                self.logger.info(f"🎯 Pre-initialized {loaded_count} critical agents, {len(self.available_agents) - loaded_count} will be lazy-loaded")

            # Initialize routing decision maker with loaded agents
            self.routing_decision_maker = create_routing_decision_maker(self.agent_nodes)
            self.logger.info("✅ Initialized routing decision maker")

            # Log agent capabilities for debugging
            for agent_id, agent_node in self.agent_nodes.items():
                if hasattr(agent_node, 'capabilities'):
                    self.logger.debug(f"Agent {agent_id} capabilities: {getattr(agent_node, 'capabilities', [])}")

        except Exception as e:
            self.logger.error(f"❌ Error initializing agents: {e}", exc_info=True)
            # Ensure at least concierge agent is available as fallback
            if not self.agent_nodes:
                self.logger.warning("⚠️ No agents initialized, attempting fallback initialization")
                try:
                    self._initialize_fallback_agents()
                except Exception as fallback_error:
                    self.logger.error(f"❌ Fallback agent initialization failed: {fallback_error}", exc_info=True)
                    # Create emergency minimal agent registry
                    self._create_emergency_agent_registry()

            # Ensure routing decision maker is initialized even if agent initialization fails
            if not self.routing_decision_maker:
                self.logger.warning("⚠️ Initializing routing decision maker with available agents")
                try:
                    self.routing_decision_maker = create_routing_decision_maker(self.agent_nodes)
                    self.logger.info("✅ Fallback routing decision maker initialized")
                except Exception as routing_error:
                    self.logger.error(f"❌ Failed to initialize routing decision maker: {routing_error}", exc_info=True)
                    # Create emergency routing decision maker
                    self._create_emergency_routing_decision_maker()

    def _create_emergency_agent_registry(self) -> None:
        """Create emergency minimal agent registry when all else fails."""
        try:
            self.logger.warning("🚨 Creating emergency agent registry")

            # Create a minimal mock agent that can handle basic responses
            class EmergencyAgent:
                def __init__(self, agent_id: str):
                    self.agent_id = agent_id
                    self.agent_type = "emergency"
                    self.capabilities = ["emergency_response"]

                async def process_message(self, message: str, user_id: str = None,
                                        conversation_id: str = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
                    return {
                        "message": "I apologize, but I'm experiencing technical difficulties. Please try again later or contact support.",
                        "metadata": {
                            "agent_type": "emergency",
                            "error_mode": True,
                            "timestamp": datetime.now().isoformat()
                        },
                        "success": True
                    }

            # Create emergency concierge agent
            emergency_agent = EmergencyAgent("emergency-concierge")

            # Wrap in BaseAgentNode-like structure
            from ..nodes.base_agent_node import BaseAgentNode

            class EmergencyAgentNode(BaseAgentNode):
                def __init__(self, agent_id: str):
                    self.agent_id = agent_id
                    self.agent_type = "emergency"
                    self.agent_instance = EmergencyAgent(agent_id)
                    self.config = {"max_agent_executions": 1}
                    self.capabilities = ["emergency_response"]
                    self.supported_intents = ["emergency"]
                    self.tools = []

                async def _process_message(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
                    # Add emergency response message
                    emergency_message = {
                        "id": str(uuid.uuid4()),
                        "content": "I'm experiencing technical difficulties. Please try again later.",
                        "type": MessageType.AGENT.value,
                        "timestamp": datetime.now().isoformat(),
                        "agent_id": self.agent_id,
                        "metadata": {"emergency_mode": True}
                    }
                    return add_message(state, emergency_message, MessageType.AGENT)

            self.agent_nodes["emergency-concierge"] = EmergencyAgentNode("emergency-concierge")
            self.logger.info("✅ Emergency agent registry created")

        except Exception as e:
            self.logger.error(f"❌ Failed to create emergency agent registry: {e}", exc_info=True)

    def _create_emergency_routing_decision_maker(self) -> None:
        """Create emergency routing decision maker when normal initialization fails."""
        try:
            self.logger.warning("🚨 Creating emergency routing decision maker")

            class EmergencyRoutingDecisionMaker:
                def __init__(self, agent_nodes: Dict[str, Any]):
                    self.agent_nodes = agent_nodes

                def determine_entry_point(self, state: UnifiedDatageniusState) -> str:
                    # Always route to first available agent or emergency fallback
                    if self.agent_nodes:
                        return f"agent_{next(iter(self.agent_nodes.keys()))}"
                    return "END"  # No agents available

                def determine_tool_routing_target(self, state: UnifiedDatageniusState) -> str:
                    return "END"  # No tool routing in emergency mode

                def update_agent_registry(self, agent_nodes: Dict[str, Any]) -> None:
                    self.agent_nodes = agent_nodes

            self.routing_decision_maker = EmergencyRoutingDecisionMaker(self.agent_nodes)
            self.logger.info("✅ Emergency routing decision maker created")

        except Exception as e:
            self.logger.error(f"❌ Failed to create emergency routing decision maker: {e}", exc_info=True)

    def _initialize_fallback_agents(self) -> None:
        """Initialize fallback agents when dynamic discovery fails."""
        try:
            self.logger.info("🔧 Initializing fallback agents")

            # Legacy agent nodes have been removed - fallback to persona system
            self.logger.info("✅ Fallback agents will be handled by the persona system")

        except Exception as e:
            self.logger.error(f"❌ Failed to initialize fallback agents: {e}")

    def _setup_dynamic_agent_manager(self) -> None:
        """Setup connection with dynamic agent manager."""
        try:
            from .dynamic_agent_manager import dynamic_agent_manager

            # Set workflow manager reference in dynamic agent manager
            dynamic_agent_manager.workflow_manager = self

            self.logger.info("🔗 Connected to dynamic agent manager")

        except Exception as e:
            self.logger.warning(f"⚠️ Could not connect to dynamic agent manager: {e}")

    def register_agent_node(self, agent_id: str, agent_node: BaseAgentNode) -> None:
        """
        Register an agent node for use in workflows.

        Args:
            agent_id: Unique identifier for the agent
            agent_node: Agent node implementation
        """
        self.agent_nodes[agent_id] = agent_node
        self.logger.info(f"✅ Registered agent node: {agent_id}")

        # Update routing decision maker with new agent registry
        if self.routing_decision_maker:
            self.routing_decision_maker.update_agent_registry(self.agent_nodes)

        # Clear workflow cache to rebuild graphs with new agent
        self._clear_workflow_cache()

    def unregister_agent_node(self, agent_id: str) -> None:
        """
        Unregister an agent node.

        Args:
            agent_id: Unique identifier for the agent to remove
        """
        if agent_id in self.agent_nodes:
            del self.agent_nodes[agent_id]
            self.logger.info(f"🗑️ Unregistered agent node: {agent_id}")

            # Clear workflow cache to rebuild graphs without this agent
            self._clear_workflow_cache()
        else:
            self.logger.warning(f"⚠️ Agent node {agent_id} not found for unregistration")

    def refresh_agents(self) -> None:
        """
        Refresh the agent registry by re-discovering available agents.

        This method can be called to dynamically update the available agents
        without restarting the workflow manager.
        """
        try:
            self.logger.info("🔄 Refreshing agent registry")

            # Clear current agents
            old_agent_count = len(self.agent_nodes)
            self.agent_nodes.clear()

            # Re-initialize agents
            self._initialize_agents()

            new_agent_count = len(self.agent_nodes)
            self.logger.info(f"🎯 Agent refresh complete: {old_agent_count} → {new_agent_count} agents")

            # Clear workflow cache to rebuild with updated agents
            self._clear_workflow_cache()

        except Exception as e:
            self.logger.error(f"❌ Error refreshing agents: {e}")

    def _load_personas_directly(self, agent_factory):
        """Load persona configurations directly from files without async dependencies."""
        try:
            import yaml
            from pathlib import Path

            # Get the personas directory path
            current_dir = Path(__file__).parent.parent
            personas_dir = current_dir / "config" / "personas"

            if not personas_dir.exists():
                self.logger.warning(f"Personas directory does not exist: {personas_dir}")
                return

            loaded_count = 0

            # Load YAML files
            for yaml_file in personas_dir.glob("*.yaml"):
                try:
                    with open(yaml_file, 'r', encoding='utf-8') as f:
                        config = yaml.safe_load(f)

                    if config:
                        # Use 'id' field as primary identifier (unified approach)
                        persona_id = config.get("id")
                        if persona_id:
                            # Register the agent configuration
                            agent_factory.register_agent_config(persona_id, config)
                            loaded_count += 1
                            self.logger.info(f"Registered persona directly: {persona_id}")
                        else:
                            self.logger.warning(f"No 'id' field in {yaml_file.name}")
                    else:
                        self.logger.warning(f"Empty config in {yaml_file.name}")

                except Exception as e:
                    self.logger.error(f"Failed to load {yaml_file.name}: {e}")

            # Load JSON files
            for json_file in personas_dir.glob("*.json"):
                try:
                    import json
                    with open(json_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)

                    if config:
                        # Use 'id' field as primary identifier (unified approach)
                        persona_id = config.get("id")
                        if persona_id:
                            # Register the agent configuration
                            agent_factory.register_agent_config(persona_id, config)
                            loaded_count += 1
                            self.logger.info(f"Registered persona directly: {persona_id}")
                        else:
                            self.logger.warning(f"No 'id' field in {json_file.name}")
                    else:
                        self.logger.warning(f"Empty config in {json_file.name}")

                except Exception as e:
                    self.logger.error(f"Failed to load {json_file.name}: {e}")

            self.logger.info(f"✅ Loaded {loaded_count} personas directly from configuration files")

        except Exception as e:
            self.logger.error(f"Error in direct persona loading: {e}")

    def get_available_agents(self) -> List[str]:
        """
        Get list of currently available agent IDs.

        Returns:
            List of agent IDs that are currently registered
        """
        return list(self.agent_nodes.keys())

    def get_agent_info(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a specific agent.

        Args:
            agent_id: Agent identifier

        Returns:
            Agent information dictionary or None if not found
        """
        if agent_id not in self.agent_nodes:
            return None

        agent_node = self.agent_nodes[agent_id]

        return {
            "agent_id": agent_id,
            "agent_type": getattr(agent_node, 'agent_type', 'unknown'),
            "capabilities": getattr(agent_node, 'capabilities', []),
            "supported_intents": getattr(agent_node, 'supported_intents', []),
            "tools": getattr(agent_node, 'tools', []),
            "class_name": agent_node.__class__.__name__
        }

    def _clear_workflow_cache(self) -> None:
        """Clear the workflow cache to force rebuilding with updated agents."""
        self.workflow_cache.clear()
        self.logger.debug("🧹 Cleared workflow cache")

    def register_tool_node(self, tool_name: str, tool_node: ToolExecutionNode) -> None:
        """
        Register a tool node for use in workflows.
        
        Args:
            tool_name: Name of the tool
            tool_node: Tool execution node
        """
        self.tool_nodes[tool_name] = tool_node
        self.logger.info(f"Registered tool node: {tool_name}")

    def _clean_state_for_serialization(self, state: UnifiedDatageniusState) -> UnifiedDatageniusState:
        """
        Clean state by removing non-serializable objects like functions.

        Args:
            state: State to clean

        Returns:
            Cleaned state safe for serialization
        """
        cleaned_state = copy.deepcopy(state)

        # Remove known non-serializable keys
        non_serializable_keys = ["stream_callback"]
        for key in non_serializable_keys:
            if key in cleaned_state:
                self.logger.debug(f"Removing non-serializable key from state: {key}")
                del cleaned_state[key]

        # Remove any callable objects
        keys_to_remove = []
        for key, value in cleaned_state.items():
            if callable(value):
                keys_to_remove.append(key)

        for key in keys_to_remove:
            self.logger.debug(f"Removing callable object from state: {key}")
            del cleaned_state[key]

        return cleaned_state

    def _initialize_workflow_state(
        self,
        user_id: str,
        conversation_id: str,
        selected_agent: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> UnifiedDatageniusState:
        """
        Initialize workflow with user's preferred agent or concierge default.

        This method implements the enhanced initial agent selection as specified
        in workflow.md section 2.3, ensuring proper workflow initialization with
        selected agent support and fallback to concierge when no agent is specified.

        Args:
            user_id: User identifier
            conversation_id: Conversation identifier
            selected_agent: Optional user-selected agent ID
            context: Optional additional context

        Returns:
            Initialized UnifiedDatageniusState with proper agent selection
        """
        try:
            self.logger.info(f"🚀 Initializing workflow state for user {user_id}")

            # Determine initial agent
            initial_agent = self._determine_initial_agent(selected_agent)
            self.logger.info(f"✅ Initial agent determined: {initial_agent}")

            # Create initial message if provided in context
            initial_message = None
            if context and context.get("message"):
                initial_message = {
                    "id": str(uuid.uuid4()),
                    "content": context["message"],
                    "type": MessageType.USER.value,
                    "timestamp": datetime.now().isoformat(),
                    "user_id": user_id
                }

            # Create base state
            state = create_unified_state(
                user_id=user_id,
                conversation_id=conversation_id,
                workflow_type=context.get("workflow_type", "default") if context else "default",
                initial_message=initial_message,
                business_profile_id=context.get("business_profile_id") if context else None
            )

            # Set selected agent and conversation mode
            from ..states.unified_state import set_selected_agent, ConversationMode
            state = set_selected_agent(state, initial_agent, ConversationMode.CONVERSATION)

            # Apply additional context if provided
            if context:
                # Update conversation context
                state["conversation_context"].update(context.get("conversation_context", {}))

                # Update user preferences
                state["user_preferences"].update(context.get("user_preferences", {}))

                # Add attached files
                state["attached_files"].extend(context.get("attached_files", []))

                # Add data sources
                state["data_sources"].extend(context.get("data_sources", []))

                # Set current persona from context
                if "current_persona" in context:
                    state["current_persona"] = context["current_persona"]
                    self.logger.info(f"🔄 Set current_persona: {context['current_persona']}")

            # Set available agents
            state["available_agents"] = list(self.agent_nodes.keys())

            # Initialize agent coordination log
            state["agent_coordination_log"] = []

            # Add initialization record
            initialization_record = {
                "timestamp": datetime.now().isoformat(),
                "action": "workflow_initialized",
                "initial_agent": initial_agent,
                "selected_agent": selected_agent,
                "user_id": user_id,
                "conversation_id": conversation_id
            }
            state["agent_coordination_log"].append(initialization_record)

            self.logger.info(f"🎯 Workflow state initialized with agent: {initial_agent}")
            return state

        except Exception as e:
            self.logger.error(f"❌ Error initializing workflow state: {e}", exc_info=True)
            # Return minimal fallback state
            return create_unified_state(
                user_id=user_id,
                conversation_id=conversation_id,
                workflow_type="default"
            )

    def _determine_initial_agent(self, selected_agent: Optional[str] = None) -> str:
        """
        Determine the initial agent for workflow entry.

        This implements the agent selection priority as specified in workflow.md:
        1. Use user's selected agent if valid
        2. Fallback to concierge agent
        3. Fallback to first available agent

        Args:
            selected_agent: Optional user-selected agent ID

        Returns:
            Agent ID to use as initial agent
        """
        # Priority 1: Use selected agent if valid
        if selected_agent and selected_agent in self.agent_nodes:
            self.logger.info(f"✅ Using user-selected agent: {selected_agent}")
            return selected_agent

        if selected_agent:
            self.logger.warning(f"⚠️ Selected agent '{selected_agent}' not available, falling back")

        # Priority 2: Fallback to concierge agent
        for agent_id in self.agent_nodes.keys():
            if "concierge" in agent_id.lower():
                self.logger.info(f"✅ Using concierge fallback: {agent_id}")
                return agent_id

        # Priority 3: Use first available agent
        if self.agent_nodes:
            fallback_agent = next(iter(self.agent_nodes.keys()))
            self.logger.info(f"✅ Using first available agent: {fallback_agent}")
            return fallback_agent

        # This should not happen in normal operation
        self.logger.error("❌ No agents available for initialization")
        raise ValueError("No agents available for workflow initialization")

    async def create_workflow(
        self,
        user_id: str,
        conversation_id: str,
        message: str,
        context: Optional[Dict[str, Any]] = None,
        workflow_type: str = "default",
        selected_agent: Optional[str] = None
    ) -> str:
        """
        Create a new workflow for processing a user message.

        Enhanced to support user-centric routing with initial agent selection.

        Args:
            user_id: User identifier
            conversation_id: Conversation identifier
            message: User message to process
            context: Optional additional context
            workflow_type: Type of workflow to create
            selected_agent: Optional user-selected agent for initial routing

        Returns:
            Workflow ID
        """
        try:
            # Prepare context with message and workflow type
            enhanced_context = context.copy() if context else {}
            enhanced_context.update({
                "message": message,
                "workflow_type": workflow_type
            })

            # Initialize workflow state with enhanced agent selection
            state = self._initialize_workflow_state(
                user_id=user_id,
                conversation_id=conversation_id,
                selected_agent=selected_agent or enhanced_context.get("selected_agent"),
                context=enhanced_context
            )

            # Clean state for serialization before building graph
            cleaned_state = self._clean_state_for_serialization(state)

            # Build workflow graph
            workflow_graph = await self._build_workflow_graph(cleaned_state)

            # Get workflow ID from state
            workflow_id = cleaned_state["workflow_id"]

            # Phase 3: Apply AI-powered optimization
            if self.phase3_enabled:
                try:
                    workflow_config = {
                        'workflow_id': workflow_id,
                        'workflow_type': enhanced_context.get('workflow_type', 'general'),
                        'agents_involved': list(self.agent_nodes.keys()),
                        'estimated_duration': 30.0,
                        'user_id': user_id,
                        'selected_agent': selected_agent
                    }

                    optimization_results = await self.phase3_service.optimize_workflow(
                        workflow_id, workflow_config
                    )

                    if optimization_results.get('optimizations_applied'):
                        self.logger.info(f"🤖 Applied Phase 3 optimizations: {optimization_results['optimizations_applied']}")
                        cleaned_state['phase3_optimizations'] = optimization_results

                except Exception as e:
                    self.logger.warning(f"⚠️ Phase 3 optimization failed: {e}")

            # Cache the workflow
            self.workflow_cache[workflow_id] = workflow_graph

            # Save initial state to checkpointer
            await self.checkpointer.save_state(workflow_id, cleaned_state)
            self.logger.info(f"Saved initial state for workflow {workflow_id}")

            # Update metrics
            self.metrics["workflows_created"] += 1

            self.logger.info(f"Created workflow {workflow_id} for user {user_id}")
            return workflow_id
            
        except Exception as e:
            self.logger.error(f"Error creating workflow: {e}", exc_info=True)
            self.metrics["workflows_failed"] += 1
            raise
    
    async def execute_workflow(
        self,
        workflow_id: str,
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Execute a workflow and return the result.
        
        Args:
            workflow_id: ID of the workflow to execute
            config: Optional execution configuration
            
        Returns:
            Workflow execution result
        """
        try:
            start_time = datetime.now()
            
            # Get workflow from cache
            if workflow_id not in self.workflow_cache:
                raise ValueError(f"Workflow {workflow_id} not found")
            
            workflow_graph = self.workflow_cache[workflow_id]
            
            # Create execution config
            execution_config = {
                "configurable": {
                    "thread_id": workflow_id,
                    "checkpoint_id": str(uuid.uuid4())
                }
            }
            if config:
                execution_config.update(config)
            
            # Execute the workflow
            self.logger.info(f"Executing workflow {workflow_id}")

            # Phase 3: Start workflow monitoring
            if self.phase3_enabled:
                try:
                    # Notify Phase 3 services of workflow start
                    from ..events.types import WorkflowStartedEvent
                    from ..events.event_bus import event_bus

                    await event_bus.publish(WorkflowStartedEvent(
                        workflow_id=workflow_id,
                        timestamp=datetime.now(),
                        data={
                            'workflow_id': workflow_id,
                            'workflow_type': 'execution',
                            'agents_involved': list(self.agent_nodes.keys()),
                            'estimated_duration': 30.0
                        }
                    ))
                except Exception as e:
                    self.logger.warning(f"⚠️ Phase 3 workflow start notification failed: {e}")

            # Get initial state from checkpointer
            initial_state = await self.checkpointer.get_state(workflow_id)
            if not initial_state:
                raise ValueError(f"No initial state found for workflow {workflow_id}")

            # Add workflow-level loop prevention
            max_workflow_duration = config.get("max_workflow_duration", 300)  # 5 minutes default
            max_workflow_steps = config.get("max_workflow_steps", 50)

            # Run the workflow with timeout and step limits
            try:
                result = await asyncio.wait_for(
                    workflow_graph.ainvoke(
                        initial_state,
                        config=execution_config
                    ),
                    timeout=max_workflow_duration
                )

                # Check if workflow exceeded step limits
                execution_count = result.get("execution_metrics", {}).get("agent_execution_count", 0)
                if execution_count >= max_workflow_steps:
                    self.logger.warning(f"⚠️ Workflow {workflow_id} reached maximum steps ({max_workflow_steps})")

            except asyncio.TimeoutError:
                self.logger.error(f"❌ Workflow {workflow_id} timed out after {max_workflow_duration} seconds")
                # Create timeout error result
                result = initial_state.copy()
                result["workflow_status"] = "timeout"
                result["error"] = f"Workflow timed out after {max_workflow_duration} seconds"
            
            # Calculate execution time
            execution_time = (datetime.now() - start_time).total_seconds()
            self.metrics["total_execution_time"] += execution_time
            self.metrics["workflows_completed"] += 1
            
            # Update final state
            result = update_workflow_status(result, WorkflowStatus.COMPLETED)
            result["execution_metrics"]["total_processing_time"] = execution_time
            
            # Save final state
            await self.checkpointer.save_state(workflow_id, result)

            # Phase 3: Notify completion for learning and optimization
            if self.phase3_enabled:
                try:
                    from ..events.types import WorkflowCompletedEvent

                    await event_bus.publish(WorkflowCompletedEvent(
                        workflow_id=workflow_id,
                        timestamp=datetime.now(),
                        data={
                            'workflow_id': workflow_id,
                            'execution_time': execution_time,
                            'status': 'completed',
                            'agents_used': list(self.agent_nodes.keys()),
                            'performance_metrics': result.get("execution_metrics", {})
                        }
                    ))
                except Exception as e:
                    self.logger.warning(f"⚠️ Phase 3 workflow completion notification failed: {e}")

            self.logger.info(f"Completed workflow {workflow_id} in {execution_time:.2f}s")

            return {
                "workflow_id": workflow_id,
                "status": "completed",
                "result": result,
                "execution_time": execution_time,
                "message": result.get("messages", [])[-1] if result.get("messages") else None
            }
            
        except Exception as e:
            self.logger.error(f"Error executing workflow {workflow_id}: {e}", exc_info=True)
            self.metrics["workflows_failed"] += 1

            # Phase 3: Attempt self-healing and notify failure
            if self.phase3_enabled:
                try:
                    # Notify Phase 3 services of workflow failure
                    from ..events.types import WorkflowFailedEvent

                    await event_bus.publish(WorkflowFailedEvent(
                        workflow_id=workflow_id,
                        timestamp=datetime.now(),
                        data={
                            'workflow_id': workflow_id,
                            'error': str(e),
                            'error_type': type(e).__name__,
                            'agents_involved': list(self.agent_nodes.keys())
                        }
                    ))

                    # Attempt self-healing
                    healing_result = await self.phase3_service.self_healing_system.attempt_healing(
                        workflow_id, str(e), type(e).__name__
                    )

                    if healing_result and healing_result.get('success'):
                        self.logger.info(f"🔧 Phase 3 self-healing successful for workflow {workflow_id}")
                        # Retry workflow execution after healing (with retry limit)
                        retry_count = config.get('healing_retry_count', 0)
                        max_retries = self.phase3_config.healing_retry_attempts if self.phase3_config else 1

                        if retry_count < max_retries:
                            config['healing_retry_count'] = retry_count + 1
                            self.logger.info(f"🔄 Retrying workflow {workflow_id} after healing (attempt {retry_count + 1}/{max_retries})")
                            return await self.execute_workflow(workflow_id, config)
                        else:
                            self.logger.warning(f"⚠️ Maximum healing retries ({max_retries}) exceeded for workflow {workflow_id}")

                except Exception as healing_error:
                    self.logger.warning(f"⚠️ Phase 3 self-healing failed: {healing_error}")

            # Update state with error
            if workflow_id in self.workflow_cache:
                try:
                    error_state = await self.checkpointer.get_state(workflow_id)
                    if error_state:
                        error_state = update_workflow_status(error_state, WorkflowStatus.FAILED)
                        error_state["error_history"].append({
                            "timestamp": datetime.now().isoformat(),
                            "error": str(e),
                            "type": "workflow_execution_error"
                        })
                        await self.checkpointer.save_state(workflow_id, error_state)
                except Exception as save_error:
                    self.logger.error(f"Error saving error state: {save_error}")
            
            return {
                "workflow_id": workflow_id,
                "status": "failed",
                "error": str(e),
                "message": "I'm sorry, I encountered an error processing your request."
            }
    
    async def _build_workflow_graph(self, state: UnifiedDatageniusState) -> StateGraph:
        """
        Build a workflow graph based on the current state.

        Args:
            state: Current workflow state

        Returns:
            Constructed workflow graph
        """
        # Ensure agents are available before building workflow
        if not self.agent_nodes:
            self.logger.warning("⚠️ No agents available when building workflow, attempting to initialize with eager loading")
            self._initialize_agents(eager_load_all=True)

            # If still no agents, this is a critical error
            if not self.agent_nodes:
                self.logger.error("❌ Critical error: No agents available after initialization attempt")
                raise ValueError("No agents available for workflow construction")

        # For workflow stability, ensure all available agents are loaded
        elif len(self.agent_nodes) < len(getattr(self, 'available_agents', [])):
            self.logger.info("🔄 Loading remaining agents for workflow stability")
            missing_agents = set(getattr(self, 'available_agents', [])) - set(self.agent_nodes.keys())
            for agent_id in missing_agents:
                try:
                    await self._ensure_agent_loaded(agent_id)
                except Exception as e:
                    self.logger.warning(f"⚠️ Could not load agent {agent_id}: {e}")

        self.logger.info(f"🔧 Building workflow with {len(self.agent_nodes)} agents: {list(self.agent_nodes.keys())}")

        # Select optimal workflow template based on context
        selected_agent = state.get("current_agent") or state.get("selected_agent")
        agent_type = self._determine_agent_type(selected_agent)

        template, confidence = template_selector.select_template(
            agent_type=agent_type,
            state=state,
            context=state.get("context", {})
        )

        self.logger.info(f"🎯 Selected workflow template '{template['template_id']}' with confidence {confidence:.3f}")

        # Apply template configuration to workflow
        workflow_config = template.get("workflow_config", {})

        # Create the graph with template-based configuration
        workflow = StateGraph(UnifiedDatageniusState)

        # Add agent switch node (for explicit agent switches only)
        # Using AgentSwitchNode instead of RoutingNode for Phase 2 implementation
        agent_switch_node = AgentSwitchNode(self.agent_nodes)
        workflow.add_node("agent_switch", agent_switch_node.execute)

        # Keep routing node for backward compatibility
        routing_node = RoutingNode(self.agent_nodes)
        workflow.add_node("routing", routing_node.execute)

        # Add agent nodes
        for agent_id, agent_node in self.agent_nodes.items():
            workflow.add_node(f"agent_{agent_id}", agent_node.execute)
            self.logger.debug(f"✅ Added agent node: agent_{agent_id}")

        # Add tool nodes
        for tool_name, tool_node in self.tool_nodes.items():
            workflow.add_node(f"tool_{tool_name}", tool_node.execute)
            self.logger.debug(f"✅ Added tool node: tool_{tool_name}")

        # Use conditional entry point based on selected agent (user-centric routing)
        routing_map = {f"agent_{agent_id}": f"agent_{agent_id}" for agent_id in self.agent_nodes.keys()}
        routing_map["agent_switch"] = "agent_switch"  # For explicit agent switches
        routing_map["routing"] = "routing"  # Fallback for backward compatibility

        workflow.add_conditional_edges(START, self._get_entry_point, routing_map)

        # Add agent switch edges (routes to selected agent after switch)
        agent_switch_routing_map = {agent_id: f"agent_{agent_id}" for agent_id in self.agent_nodes.keys()}
        workflow.add_conditional_edges(
            "agent_switch",
            self._route_to_agent,
            agent_switch_routing_map
        )

        # Add routing edges (for backward compatibility)
        routing_map = {agent_id: f"agent_{agent_id}" for agent_id in self.agent_nodes.keys()}
        self.logger.info(f"🔧 Building workflow with routing map: {routing_map}")

        workflow.add_conditional_edges(
            "routing",
            self._route_to_agent,
            routing_map
        )
        
        # Add agent to agent/tool/agent_switch/end edges (supports Command pattern routing)
        for agent_id in self.agent_nodes.keys():
            workflow.add_conditional_edges(
                f"agent_{agent_id}",
                self._route_from_agent,
                {
                    **{f"agent_{other_agent_id}": f"agent_{other_agent_id}" for other_agent_id in self.agent_nodes.keys()},
                    **{tool_name: f"tool_{tool_name}" for tool_name in self.tool_nodes.keys()},
                    "agent_switch": "agent_switch",  # Allow routing to agent switch node
                    "routing": "routing",  # Backward compatibility
                    "END": END
                }
            )
        
        # Add tool back to agent edges (user-centric routing)
        # Tools now route back to the selected agent instead of routing node
        for tool_name in self.tool_nodes.keys():
            workflow.add_conditional_edges(
                f"tool_{tool_name}",
                self._route_from_tool,
                {f"agent_{agent_id}": f"agent_{agent_id}" for agent_id in self.agent_nodes.keys()}
            )
        
        # Compile the graph
        compiled_graph = workflow.compile(checkpointer=self.memory_saver)
        
        return compiled_graph

    def _get_entry_point(self, state: UnifiedDatageniusState) -> str:
        """
        Determine entry point based on selected agent (user-centric routing).

        This implements the user-centric architecture where workflows start
        directly at the user's selected agent instead of routing analysis.

        Args:
            state: Current workflow state containing agent selection information

        Returns:
            Entry point node name in format "agent_{agent_id}" or "routing"

        Raises:
            ValueError: If routing decision maker is not initialized
        """
        try:
            # Ensure routing decision maker is initialized
            self._ensure_routing_decision_maker()

            # Use centralized routing decision maker
            if not self.routing_decision_maker:
                self.logger.error("❌ Routing decision maker still not initialized after ensure call")
                return RoutingConstants.ROUTING_FALLBACK

            return self.routing_decision_maker.determine_entry_point(state)

        except ValidationError as e:
            self.logger.error(f"❌ Validation error in _get_entry_point: {e}")
            return RoutingConstants.ROUTING_FALLBACK

        except AgentNotFoundError as e:
            self.logger.error(f"❌ Agent not found in _get_entry_point: {e}")
            return RoutingConstants.ROUTING_FALLBACK

        except Exception as e:
            self.logger.error(f"❌ Unexpected error in _get_entry_point: {e}", exc_info=True)
            return RoutingConstants.ROUTING_FALLBACK

    def _route_from_tool(self, state: UnifiedDatageniusState) -> str:
        """
        Route from tool back to the currently selected agent (user-centric routing).

        This implements the user-centric architecture where tools route back
        to the selected agent instead of the routing node, maintaining
        conversation continuity.

        Args:
            state: Current workflow state containing agent selection information

        Returns:
            Target agent node name in format "agent_{agent_id}"

        Raises:
            ValueError: If routing decision maker is not initialized or no agents available
        """
        try:
            # Use centralized routing decision maker
            if not self.routing_decision_maker:
                self.logger.error("❌ Routing decision maker not initialized")
                raise ValueError("Routing decision maker not initialized")

            return self.routing_decision_maker.determine_tool_routing_target(state)

        except ValidationError as e:
            self.logger.error(f"❌ Validation error in _route_from_tool: {e}")
            raise ValueError(f"Tool routing validation failed: {e}")

        except AgentNotFoundError as e:
            self.logger.error(f"❌ Agent not found in _route_from_tool: {e}")
            raise ValueError(f"No agents available for tool routing: {e}")

        except Exception as e:
            self.logger.error(f"❌ Unexpected error in _route_from_tool: {e}", exc_info=True)
            raise ValueError(f"Tool routing failed: {e}")

    def _route_to_agent(self, state: UnifiedDatageniusState) -> str:
        """
        Route to appropriate agent using the centralized routing decision maker.

        This method now uses the proper routing decision maker instead of
        duplicating routing logic, which was causing the infinite loop issue.
        """
        self.logger.info(f"🎯 _route_to_agent called with available agents: {list(self.agent_nodes.keys())}")

        # Critical check: Ensure agents are available
        if not self.agent_nodes:
            self.logger.error("❌ No agents available for routing - attempting emergency initialization")
            try:
                self._initialize_agents()
                if not self.agent_nodes:
                    self.logger.error("❌ Emergency initialization failed - no agents available")
                    raise ValueError("No agents available for routing and no fallback possible")
                else:
                    self.logger.info(f"✅ Emergency initialization successful - {len(self.agent_nodes)} agents available")
            except Exception as e:
                self.logger.error(f"❌ Emergency initialization failed: {e}")
                raise ValueError("No agents available for routing and no fallback possible")

        # Validate state parameter
        if state is None:
            self.logger.error("❌ State is None in _route_to_agent - using emergency fallback")
            return self._get_emergency_fallback_agent()

        # Use the centralized routing decision maker for entry point determination
        try:
            self.logger.debug(f"🔍 Using routing decision maker for agent routing")
            self.logger.debug(f"🔍 State selected_agent: {state.get('selected_agent')}")
            self.logger.debug(f"🔍 State current_agent: {state.get('current_agent')}")

            # Use the proper entry point determination
            entry_point = self._get_entry_point(state)

            # Extract agent ID from entry point (remove "agent_" prefix if present)
            if entry_point.startswith("agent_"):
                agent_id = entry_point[6:]  # Remove "agent_" prefix
            else:
                agent_id = entry_point

            self.logger.info(f"🎯 Routing decision maker selected: {agent_id}")
            return agent_id

        except Exception as e:
            self.logger.error(f"❌ Error in routing decision maker: {e}", exc_info=True)
            return self._get_emergency_fallback_agent()

    def _get_emergency_fallback_agent(self) -> str:
        """Get emergency fallback agent when routing fails."""
        # Emergency fallback to concierge
        for agent_id in self.agent_nodes.keys():
            if "concierge" in agent_id.lower():
                self.logger.warning(f"🚨 Emergency fallback to concierge: {agent_id}")
                return agent_id

        # If no concierge, return first available
        if self.agent_nodes:
            fallback = next(iter(self.agent_nodes.keys()))
            self.logger.warning(f"🚨 Emergency fallback to first agent: {fallback}")
            return fallback

        raise ValueError("No agents available for emergency fallback")

    def _route_from_agent(self, state: UnifiedDatageniusState) -> str:
        """
        Route from agent based on LangGraph Command pattern or tool requirements.

        This method handles Command objects returned by agents and routes accordingly.
        """
        # Validate state parameter
        if state is None:
            self.logger.error("❌ State is None in _route_from_agent - ending workflow")
            return "END"

        try:
            # Check if there's a command in the state (from agent Command pattern)
            agent_command = state.get("agent_command")
            if agent_command and hasattr(agent_command, 'goto'):
                target = agent_command.goto
                self.logger.info(f"🎯 WorkflowManager: Following Command routing to: {target}")

                # Route to agent
                if target in self.agent_nodes:
                    return f"agent_{target}"

                # Route to tool
                if target in self.tool_nodes:
                    return target

                # Route to end
                if target == "END":
                    return "END"

            # Check for tool execution requirements
            pending_tools = state.get("pending_tools", [])
            if pending_tools:
                next_tool = pending_tools[0]
                if next_tool in self.tool_nodes:
                    self.logger.info(f"🔧 WorkflowManager: Routing to tool: {next_tool}")
                    return next_tool

            # Check if conversation should continue with selected agent
            from ..states.unified_state import should_route_to_selected_agent, get_routing_target

            # Only continue if the agent hasn't produced a response yet
            if should_route_to_selected_agent(state):
                selected_agent = get_routing_target(state)
                if selected_agent and selected_agent in self.agent_nodes:

                    # Check workflow circuit breaker before continuing
                    workflow_id = state.get("workflow_id", "unknown")
                    if self._check_workflow_circuit_breaker(workflow_id, state):
                        self.logger.error(f"🚨 Workflow circuit breaker triggered for {workflow_id}")
                        return "END"
                    # Check if the agent has already produced a response
                    messages = state.get("messages", [])
                    current_message_id = state.get("current_message_id")

                    # Look for an agent response to the current message
                    agent_responded = False
                    if current_message_id:
                        self.logger.info(f"🔍 Checking for agent response to message ID: {current_message_id}")
                        for msg in reversed(messages):
                            msg_type = msg.get("type")
                            msg_agent_id = msg.get("agent_id")
                            msg_in_response_to = msg.get("in_response_to")

                            self.logger.debug(f"🔍 Message: type={msg_type}, agent_id={msg_agent_id}, in_response_to={msg_in_response_to}")

                            # Enhanced agent ID matching to handle different agent ID formats
                            agent_id_match = self._is_agent_id_match(msg_agent_id, selected_agent)

                            if (msg_type == MessageType.AGENT.value and
                                agent_id_match and
                                msg_in_response_to == current_message_id):
                                agent_responded = True
                                self.logger.info(f"✅ Found agent response from {msg_agent_id} (matches {selected_agent}) to message {current_message_id}")
                                break
                    else:
                        self.logger.warning("⚠️ No current_message_id found in state")

                    # Check for workflow completion flags
                    workflow_complete = state.get("workflow_complete", False)
                    next_action = state.get("next_action")

                    if workflow_complete or next_action == "END":
                        self.logger.info(f"🏁 WorkflowManager: Workflow marked as complete or next_action is END")
                        return "END"

                    # If agent hasn't responded yet, continue with the agent
                    if not agent_responded:
                        self.logger.info(f"✅ WorkflowManager: Continuing with selected agent: {selected_agent}")
                        return f"agent_{selected_agent}"
                    else:
                        self.logger.info(f"🏁 WorkflowManager: Agent {selected_agent} has responded, ending workflow")
                        return "END"

        except Exception as e:
            self.logger.error(f"❌ Error in _route_from_agent: {e}", exc_info=True)

        # Default to END if no routing decision
        self.logger.info("🏁 WorkflowManager: No routing target found, ending workflow")
        return "END"

    def _is_agent_id_match(self, msg_agent_id: str, selected_agent: str) -> bool:
        """
        Check if message agent ID matches the selected agent, handling different ID formats.

        This fixes the infinite loop issue by properly matching agent IDs that may have
        different formats (e.g., "user_selected_concierge" vs "concierge-agent" vs "concierge").

        Args:
            msg_agent_id: Agent ID from the message
            selected_agent: Selected agent ID to match against

        Returns:
            True if the agent IDs match, False otherwise
        """
        if not msg_agent_id or not selected_agent:
            return False

    def _check_workflow_circuit_breaker(self, workflow_id: str, state: UnifiedDatageniusState) -> bool:
        """
        Check if workflow should be terminated due to circuit breaker conditions.

        This prevents infinite loops at the workflow level by tracking:
        1. Total agent executions per workflow
        2. Consecutive executions of the same agent
        3. Rapid execution patterns

        Args:
            workflow_id: Workflow identifier
            state: Current workflow state

        Returns:
            True if workflow should be terminated, False otherwise
        """
        try:
            # Initialize circuit breaker for this workflow if needed
            if workflow_id not in self.workflow_circuit_breakers:
                self.workflow_circuit_breakers[workflow_id] = {
                    "total_executions": 0,
                    "agent_execution_history": [],
                    "last_execution_time": None,
                    "consecutive_same_agent": 0,
                    "last_agent": None
                }

            breaker = self.workflow_circuit_breakers[workflow_id]
            current_agent = state.get("current_agent", "")

            # Update execution count
            breaker["total_executions"] += 1
            breaker["agent_execution_history"].append({
                "agent": current_agent,
                "timestamp": datetime.now().isoformat()
            })

            # Keep only recent history (last 20 executions)
            if len(breaker["agent_execution_history"]) > 20:
                breaker["agent_execution_history"] = breaker["agent_execution_history"][-20:]

            # Check for too many total executions
            if breaker["total_executions"] > self.max_agent_executions_per_workflow:
                self.logger.error(f"🚨 Circuit breaker: Workflow {workflow_id} exceeded max executions ({self.max_agent_executions_per_workflow})")
                return True

            # Check for consecutive same agent executions
            if current_agent == breaker["last_agent"]:
                breaker["consecutive_same_agent"] += 1
            else:
                breaker["consecutive_same_agent"] = 1
                breaker["last_agent"] = current_agent

            if breaker["consecutive_same_agent"] > self.max_same_agent_consecutive_executions:
                self.logger.error(f"🚨 Circuit breaker: Agent {current_agent} executed {breaker['consecutive_same_agent']} times consecutively in workflow {workflow_id}")
                return True

            # Check for rapid execution pattern (more than 5 executions in 10 seconds)
            now = datetime.now()
            recent_executions = [
                exec_info for exec_info in breaker["agent_execution_history"]
                if (now - datetime.fromisoformat(exec_info["timestamp"])).total_seconds() < 10
            ]

            if len(recent_executions) > 5:
                self.logger.error(f"🚨 Circuit breaker: Rapid execution pattern detected in workflow {workflow_id} ({len(recent_executions)} executions in 10 seconds)")
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error in workflow circuit breaker check: {e}")
            # Fail safe - terminate workflow on error
            return True

    def _route_to_tool_or_end(self, state: UnifiedDatageniusState) -> str:
        """Route to tool execution or end workflow."""
        # Check if any tools need to be executed
        pending_tools = [
            tool for tool, status in state.get("tool_status", {}).items()
            if status == "pending"
        ]
        
        if pending_tools:
            return pending_tools[0]  # Execute first pending tool
        
        return "END"
    
    async def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """
        Get the current status of a workflow.
        
        Args:
            workflow_id: ID of the workflow
            
        Returns:
            Workflow status information
        """
        try:
            state = await self.checkpointer.get_state(workflow_id)
            if not state:
                return {"status": "not_found"}
            
            return {
                "workflow_id": workflow_id,
                "status": state["workflow_status"],
                "phase": state["workflow_phase"],
                "current_agent": state["current_agent"],
                "progress": {
                    "current_step": state["current_step"],
                    "total_steps": state["total_steps"]
                },
                "metrics": state["execution_metrics"]
            }
            
        except Exception as e:
            self.logger.error(f"Error getting workflow status: {e}")
            return {"status": "error", "error": str(e)}
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get workflow manager performance metrics."""
        return self.metrics.copy()

    async def execute_workflow_streaming(
        self,
        state: UnifiedDatageniusState,
        workflow_type: str = "default",
        stream_callback: Optional[Callable] = None
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """
        Execute workflow with streaming support.

        Args:
            state: Initial workflow state
            workflow_type: Type of workflow to execute
            stream_callback: Optional callback for streaming updates

        Yields:
            Streaming updates from workflow execution
        """
        try:
            workflow_id = state["workflow_id"]
            self.logger.info(f"Starting streaming workflow execution: {workflow_id}")

            # Clean state for serialization (remove functions and other non-serializable objects)
            cleaned_state = self._clean_state_for_serialization(state)

            # Yield initial status
            yield {
                "type": "workflow_started",
                "workflow_id": workflow_id,
                "timestamp": datetime.now().isoformat()
            }

            # Build workflow graph using cleaned state
            workflow_graph = await self._build_workflow_graph(cleaned_state)

            # Cache the workflow
            self.workflow_cache[workflow_id] = workflow_graph

            # Save initial state (cleaned)
            await self.checkpointer.save_state(workflow_id, cleaned_state)

            # Execute the actual workflow and stream the real agent responses
            current_state = cleaned_state
            step_count = 0

            # Yield routing step
            step_count += 1
            yield {
                "type": "step_started",
                "step": step_count,
                "node": "routing",
                "timestamp": datetime.now().isoformat()
            }

            # Debug state before routing
            self.logger.info(f"🔍 State before routing - selected_agent: {current_state.get('selected_agent')}")
            self.logger.info(f"🔍 State before routing - current_agent: {current_state.get('current_agent')}")
            self.logger.info(f"🔍 State before routing - conversation_mode: {current_state.get('conversation_mode')}")

            # Determine target agent
            target_agent = self._route_to_agent(current_state)

            yield {
                "type": "step_completed",
                "step": step_count,
                "node": "routing",
                "result": {"target_agent": target_agent},
                "timestamp": datetime.now().isoformat()
            }

            # Execute agent step with real processing
            step_count += 1
            yield {
                "type": "step_started",
                "step": step_count,
                "node": f"agent_{target_agent}",
                "timestamp": datetime.now().isoformat()
            }

            # Execute the actual workflow to get the real agent response
            self.logger.info(f"Executing actual workflow for streaming: {workflow_id}")
            self.logger.info(f"Target agent: {target_agent}")
            self.logger.info(f"Current state keys: {list(current_state.keys())}")

            try:
                # Execute the workflow graph directly with the current state
                final_result = await workflow_graph.ainvoke(
                    current_state,
                    config={
                        "configurable": {
                            "thread_id": workflow_id,
                            "checkpoint_id": str(uuid.uuid4())
                        }
                    }
                )
                self.logger.info(f"Workflow execution completed. Result type: {type(final_result)}")
                if final_result:
                    self.logger.info(f"Result keys: {list(final_result.keys()) if isinstance(final_result, dict) else 'Not a dict'}")
                else:
                    self.logger.error("Workflow returned None result")
            except Exception as workflow_error:
                self.logger.error(f"Workflow execution failed: {workflow_error}", exc_info=True)
                final_result = None

            # Extract the agent response from the final result and stream it
            if final_result is not None and isinstance(final_result, dict):
                # final_result is now the direct state from workflow execution
                messages = final_result.get("messages", [])
                self.logger.info(f"Found {len(messages)} messages in workflow result")

                # Find the agent response message and metadata
                agent_response = None
                agent_metadata = {}
                for message in messages:
                    if isinstance(message, dict) and message.get("type") == "agent":
                        agent_response = message.get("content", "")
                        agent_metadata = message.get("metadata", {})
                        self.logger.info(f"Found agent response with {len(agent_response)} characters")
                        break

                if agent_response:
                    # Stream the actual agent response in chunks
                    chunk_size = 30  # Slightly larger chunks for better readability
                    for i in range(0, len(agent_response), chunk_size):
                        chunk = agent_response[i:i+chunk_size]
                        yield {
                            "type": "content",
                            "content": chunk,
                            "node": f"agent_{target_agent}",
                            "timestamp": datetime.now().isoformat()
                        }
                        await asyncio.sleep(0.05)  # Shorter delay for more natural streaming

                    # Stream the agent metadata after content is complete
                    if agent_metadata:
                        self.logger.info(f"Streaming agent metadata with keys: {list(agent_metadata.keys())}")
                        if 'is_persona_recommendation' in agent_metadata:
                            self.logger.info(f"Streaming persona recommendation metadata")
                        yield {
                            "type": "metadata",
                            "metadata": agent_metadata,
                            "node": f"agent_{target_agent}",
                            "timestamp": datetime.now().isoformat()
                        }
                else:
                    # Fallback if no agent response found
                    self.logger.warning("No agent response found in workflow result")
                    fallback_response = "I apologize, but I couldn't generate a proper response. Please try again."
                    yield {
                        "type": "content",
                        "content": fallback_response,
                        "node": f"agent_{target_agent}",
                        "timestamp": datetime.now().isoformat()
                    }
            else:
                # Handle workflow failure or no result
                self.logger.error(f"Workflow execution failed or returned invalid result: {type(final_result)}")
                if final_result is None:
                    self.logger.error("Workflow returned None - this indicates a graph compilation or execution error")
                error_response = "I encountered an issue processing your request. Please try again."
                yield {
                    "type": "content",
                    "content": error_response,
                    "node": f"agent_{target_agent}",
                    "timestamp": datetime.now().isoformat()
                }

            # Complete agent step
            yield {
                "type": "step_completed",
                "step": step_count,
                "node": f"agent_{target_agent}",
                "timestamp": datetime.now().isoformat()
            }

            # Yield final result
            final_message = None
            if final_result and isinstance(final_result, dict) and final_result.get("messages"):
                messages = final_result.get("messages", [])
                if messages:
                    final_message = messages[-1]

            yield {
                "type": "workflow_completed",
                "workflow_id": workflow_id,
                "result": {
                    "workflow_id": workflow_id,
                    "status": "completed",
                    "result": final_result,
                    "message": final_message
                },
                "timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error in streaming workflow execution: {e}")
            yield {
                "type": "workflow_error",
                "workflow_id": state.get("workflow_id", "unknown"),
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }
