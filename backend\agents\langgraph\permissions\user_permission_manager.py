"""
User Permission Manager

This module manages user permissions for agent collaboration in the user-controlled
collaboration system. It handles permission requests, user responses, and maintains
permission state.
"""

import asyncio
import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class PermissionStatus(Enum):
    """Status of a permission request."""
    PENDING = "pending"
    APPROVED = "approved"
    DENIED = "denied"
    EXPIRED = "expired"
    CANCELLED = "cancelled"


@dataclass
class PermissionRequest:
    """Represents a permission request for agent collaboration."""
    request_id: str
    user_id: str
    requesting_agent: str
    specialist_type: str
    task_description: str
    required_capability: str
    status: PermissionStatus = PermissionStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    response_at: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PermissionResponse:
    """Response to a permission request."""
    request_id: str
    approved: bool
    user_id: str
    response_timestamp: datetime = field(default_factory=datetime.now)
    reason: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class UserPermissionManager:
    """
    Manages user permissions for agent collaboration.
    
    This class handles:
    - Creating permission requests for agent collaboration
    - Managing permission request lifecycle
    - Handling user responses
    - Maintaining permission history
    - Providing permission analytics
    """

    def __init__(self, default_timeout_minutes: int = 5):
        """
        Initialize the permission manager.
        
        Args:
            default_timeout_minutes: Default timeout for permission requests
        """
        self.default_timeout = timedelta(minutes=default_timeout_minutes)
        
        # Active permission requests
        self.active_requests: Dict[str, PermissionRequest] = {}
        
        # Permission history
        self.permission_history: List[PermissionRequest] = []
        
        # User preferences for auto-approval
        self.user_preferences: Dict[str, Dict[str, Any]] = {}
        
        # Callbacks for permission events
        self.permission_callbacks: Dict[str, List[Callable]] = {
            'request_created': [],
            'request_approved': [],
            'request_denied': [],
            'request_expired': [],
            'request_cancelled': []
        }
        
        # Background cleanup task
        self._cleanup_task = None
        self._start_cleanup_task()
        
        logger.info("UserPermissionManager initialized")

    async def request_collaboration_permission(
        self,
        user_id: str,
        requesting_agent: str,
        specialist_type: str,
        task_description: str,
        required_capability: str,
        timeout_minutes: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        Request user permission for agent collaboration.
        
        Args:
            user_id: ID of the user to request permission from
            requesting_agent: ID of the agent requesting collaboration
            specialist_type: Type of specialist needed
            task_description: Description of the task requiring collaboration
            required_capability: Specific capability needed
            timeout_minutes: Custom timeout for this request
            metadata: Additional metadata for the request
            
        Returns:
            True if permission is granted, False otherwise
        """
        try:
            # Check user preferences for auto-approval
            if self._check_auto_approval(user_id, required_capability, specialist_type):
                logger.info(f"Auto-approval granted for user {user_id}, capability {required_capability}")
                return True
            
            # Create permission request
            request_id = str(uuid.uuid4())
            timeout = timedelta(minutes=timeout_minutes or self.default_timeout.total_seconds() / 60)
            expires_at = datetime.now() + timeout
            
            permission_request = PermissionRequest(
                request_id=request_id,
                user_id=user_id,
                requesting_agent=requesting_agent,
                specialist_type=specialist_type,
                task_description=task_description,
                required_capability=required_capability,
                expires_at=expires_at,
                metadata=metadata or {}
            )
            
            # Store the request
            self.active_requests[request_id] = permission_request
            
            logger.info(f"Created permission request {request_id} for user {user_id}")
            
            # Trigger callbacks
            await self._trigger_callbacks('request_created', permission_request)
            
            # Send permission request to user (this would integrate with frontend)
            await self._send_permission_request_to_user(permission_request)
            
            # Wait for user response or timeout
            response = await self._wait_for_permission_response(request_id, timeout)
            
            if response:
                return response.approved
            else:
                # Request expired
                permission_request.status = PermissionStatus.EXPIRED
                await self._trigger_callbacks('request_expired', permission_request)
                logger.warning(f"Permission request {request_id} expired")
                return False
                
        except Exception as e:
            logger.error(f"Error requesting collaboration permission: {e}")
            return False

    async def handle_permission_response(self, response: PermissionResponse) -> bool:
        """
        Handle a user's response to a permission request.
        
        Args:
            response: The user's permission response
            
        Returns:
            True if response was processed successfully
        """
        try:
            request_id = response.request_id
            
            if request_id not in self.active_requests:
                logger.warning(f"Permission request {request_id} not found or already processed")
                return False
            
            permission_request = self.active_requests[request_id]
            
            # Check if request has expired
            if permission_request.expires_at and datetime.now() > permission_request.expires_at:
                permission_request.status = PermissionStatus.EXPIRED
                logger.warning(f"Permission request {request_id} has expired")
                return False
            
            # Update request with response
            permission_request.status = PermissionStatus.APPROVED if response.approved else PermissionStatus.DENIED
            permission_request.response_at = response.response_timestamp
            permission_request.metadata.update(response.metadata)
            
            # Move to history
            self.permission_history.append(permission_request)
            del self.active_requests[request_id]
            
            # Trigger callbacks
            callback_type = 'request_approved' if response.approved else 'request_denied'
            await self._trigger_callbacks(callback_type, permission_request)
            
            logger.info(f"Permission request {request_id} {'approved' if response.approved else 'denied'}")
            return True
            
        except Exception as e:
            logger.error(f"Error handling permission response: {e}")
            return False

    def set_user_preference(self, user_id: str, preference_type: str, value: Any) -> None:
        """
        Set a user preference for permission handling.
        
        Args:
            user_id: ID of the user
            preference_type: Type of preference (e.g., 'auto_approve_capabilities')
            value: Preference value
        """
        if user_id not in self.user_preferences:
            self.user_preferences[user_id] = {}
        
        self.user_preferences[user_id][preference_type] = value
        logger.info(f"Set user preference for {user_id}: {preference_type} = {value}")

    def get_user_preferences(self, user_id: str) -> Dict[str, Any]:
        """Get user preferences for permission handling."""
        return self.user_preferences.get(user_id, {})

    def get_permission_history(self, user_id: Optional[str] = None, 
                             limit: Optional[int] = None) -> List[PermissionRequest]:
        """
        Get permission history.
        
        Args:
            user_id: Filter by user ID (optional)
            limit: Maximum number of records to return
            
        Returns:
            List of permission requests
        """
        history = self.permission_history
        
        if user_id:
            history = [req for req in history if req.user_id == user_id]
        
        # Sort by creation time (most recent first)
        history = sorted(history, key=lambda x: x.created_at, reverse=True)
        
        if limit:
            history = history[:limit]
        
        return history

    def get_permission_stats(self, user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Get permission statistics.
        
        Args:
            user_id: Filter by user ID (optional)
            
        Returns:
            Dictionary with permission statistics
        """
        history = self.get_permission_history(user_id)
        
        total_requests = len(history)
        approved_requests = len([req for req in history if req.status == PermissionStatus.APPROVED])
        denied_requests = len([req for req in history if req.status == PermissionStatus.DENIED])
        expired_requests = len([req for req in history if req.status == PermissionStatus.EXPIRED])
        
        # Calculate approval rate
        approval_rate = (approved_requests / total_requests * 100) if total_requests > 0 else 0
        
        # Most requested capabilities
        capability_counts = {}
        for req in history:
            capability = req.required_capability
            capability_counts[capability] = capability_counts.get(capability, 0) + 1
        
        return {
            'total_requests': total_requests,
            'approved_requests': approved_requests,
            'denied_requests': denied_requests,
            'expired_requests': expired_requests,
            'approval_rate': approval_rate,
            'most_requested_capabilities': sorted(capability_counts.items(), key=lambda x: x[1], reverse=True)
        }

    def add_permission_callback(self, event_type: str, callback: Callable) -> None:
        """
        Add a callback for permission events.
        
        Args:
            event_type: Type of event ('request_created', 'request_approved', etc.)
            callback: Callback function to execute
        """
        if event_type in self.permission_callbacks:
            self.permission_callbacks[event_type].append(callback)
            logger.info(f"Added callback for {event_type} events")

    def _check_auto_approval(self, user_id: str, required_capability: str, specialist_type: str) -> bool:
        """Check if auto-approval is enabled for this request."""
        user_prefs = self.user_preferences.get(user_id, {})
        
        # Check capability-based auto-approval
        auto_approve_capabilities = user_prefs.get('auto_approve_capabilities', [])
        if required_capability in auto_approve_capabilities:
            return True
        
        # Check specialist-type-based auto-approval
        auto_approve_specialists = user_prefs.get('auto_approve_specialists', [])
        if specialist_type in auto_approve_specialists:
            return True
        
        return False

    async def _send_permission_request_to_user(self, request: PermissionRequest) -> None:
        """Send permission request to user (placeholder for frontend integration)."""
        # This would integrate with the frontend to show permission dialog
        # For now, we'll just log the request
        logger.info(f"Sending permission request to user {request.user_id}: "
                   f"Agent {request.requesting_agent} wants to collaborate with {request.specialist_type} "
                   f"to {request.task_description}")

    async def _wait_for_permission_response(self, request_id: str, timeout: timedelta) -> Optional[PermissionResponse]:
        """Wait for user response to permission request."""
        # This is a placeholder - in a real implementation, this would wait for
        # the user's response from the frontend
        try:
            await asyncio.sleep(timeout.total_seconds())
            return None  # Timeout
        except asyncio.CancelledError:
            # Request was cancelled
            return None

    async def _trigger_callbacks(self, event_type: str, request: PermissionRequest) -> None:
        """Trigger callbacks for permission events."""
        callbacks = self.permission_callbacks.get(event_type, [])
        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(request)
                else:
                    callback(request)
            except Exception as e:
                logger.error(f"Error in permission callback: {e}")

    def _start_cleanup_task(self) -> None:
        """Start background task to clean up expired requests."""
        async def cleanup_expired_requests():
            while True:
                try:
                    await asyncio.sleep(60)  # Check every minute
                    current_time = datetime.now()
                    expired_requests = []
                    
                    for request_id, request in list(self.active_requests.items()):
                        if request.expires_at and current_time > request.expires_at:
                            expired_requests.append(request_id)
                    
                    for request_id in expired_requests:
                        request = self.active_requests.pop(request_id)
                        request.status = PermissionStatus.EXPIRED
                        self.permission_history.append(request)
                        await self._trigger_callbacks('request_expired', request)
                        logger.info(f"Cleaned up expired permission request {request_id}")
                        
                except Exception as e:
                    logger.error(f"Error in permission cleanup task: {e}")
        
        self._cleanup_task = asyncio.create_task(cleanup_expired_requests())

    async def shutdown(self) -> None:
        """Shutdown the permission manager and cleanup resources."""
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        logger.info("UserPermissionManager shutdown complete")
