"""
Comprehensive tests for the LangGraph Network Architecture.

This module tests the network of agents implementation including:
- Agent registry and discovery
- Inter-agent communication
- Network routing and orchestration
- Consensus mechanisms
- Network intelligence
"""

import pytest
import asyncio
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Any, List
import uuid

from ..core.agent_registry import <PERSON>AgentReg<PERSON><PERSON>, NetworkAgent, AgentStatus
from ..core.agent_discovery import AgentDiscoveryService, DiscoveryRequest, DiscoveryStrategy
from ..communication.messaging_system import InterAgentMessagingSystem, NetworkCommunicationType, NetworkMessagePriority
from ..core.network_graph_builder import NetworkGraphBuilder
from ..orchestration.network_workflow_manager import NetworkWorkflowManager, WorkflowPattern
from ..consensus.consensus_mechanisms import Consensus<PERSON><PERSON><PERSON>, ConsensusType
from ..intelligence.network_intelligence import NetworkIntelligenceSystem, InsightType
from ..states.network_state import NetworkDatageniusState, create_network_state


class TestNetworkAgentRegistry:
    """Test the network agent registry functionality."""

    @pytest.fixture
    def registry(self):
        """Create a fresh registry for testing."""
        return NetworkAgentRegistry()

    @pytest.mark.asyncio
    async def test_agent_registration(self, registry):
        """Test agent registration and retrieval."""
        # Register an agent
        success = await registry.register_agent(
            agent_id="test_agent_1",
            agent_type="concierge",
            name="Test Concierge",
            description="Test concierge agent",
            capabilities=[
                {
                    "name": "conversation",
                    "description": "Handle conversations",
                    "confidence_level": 0.9
                }
            ],
            communication_capabilities=["direct_message", "consultation"]
        )
        
        assert success is True
        
        # Retrieve the agent
        agent = registry.get_agent("test_agent_1")
        assert agent is not None
        assert agent.agent_id == "test_agent_1"
        assert agent.agent_type == "concierge"
        assert agent.name == "Test Concierge"
        assert len(agent.capabilities) == 1
        assert agent.capabilities[0].name == "conversation"

    @pytest.mark.asyncio
    async def test_capability_based_search(self, registry):
        """Test finding agents by capability."""
        # Register multiple agents with different capabilities
        await registry.register_agent(
            agent_id="agent_1",
            agent_type="analysis",
            name="Data Analyst",
            description="Data analysis agent",
            capabilities=[
                {"name": "data_analysis", "confidence_level": 0.9},
                {"name": "visualization", "confidence_level": 0.8}
            ]
        )
        
        await registry.register_agent(
            agent_id="agent_2",
            agent_type="marketing",
            name="Marketing Expert",
            description="Marketing agent",
            capabilities=[
                {"name": "marketing_strategy", "confidence_level": 0.9},
                {"name": "content_creation", "confidence_level": 0.7}
            ]
        )
        
        # Find agents with data analysis capability
        data_agents = registry.find_agents_by_capability("data_analysis")
        assert len(data_agents) == 1
        assert data_agents[0].agent_id == "agent_1"
        
        # Find agents with marketing capability
        marketing_agents = registry.find_agents_by_capability("marketing_strategy")
        assert len(marketing_agents) == 1
        assert marketing_agents[0].agent_id == "agent_2"

    @pytest.mark.asyncio
    async def test_agent_status_updates(self, registry):
        """Test agent status management."""
        # Register an agent
        await registry.register_agent(
            agent_id="status_test_agent",
            agent_type="test",
            name="Status Test Agent",
            description="Agent for testing status updates",
            capabilities=[{"name": "testing", "confidence_level": 0.8}]
        )
        
        # Check initial status
        agent = registry.get_agent("status_test_agent")
        assert agent.status == AgentStatus.IDLE
        
        # Update status to active
        success = await registry.update_agent_status("status_test_agent", AgentStatus.ACTIVE)
        assert success is True
        
        agent = registry.get_agent("status_test_agent")
        assert agent.status == AgentStatus.ACTIVE
        
        # Update load
        success = await registry.update_agent_load("status_test_agent", 2)
        assert success is True
        
        agent = registry.get_agent("status_test_agent")
        assert agent.current_load == 2


class TestAgentDiscoveryService:
    """Test the agent discovery service."""

    @pytest.fixture
    def discovery_service(self):
        """Create discovery service with test registry."""
        registry = NetworkAgentRegistry()
        return AgentDiscoveryService(registry)

    @pytest.mark.asyncio
    async def test_capability_based_discovery(self, discovery_service):
        """Test capability-based agent discovery."""
        # Register test agents
        await discovery_service.registry.register_agent(
            agent_id="specialist_1",
            agent_type="analysis",
            name="Data Specialist",
            description="Specialized in data analysis",
            capabilities=[
                {"name": "data_analysis", "confidence_level": 0.95},
                {"name": "statistics", "confidence_level": 0.9}
            ]
        )
        
        await discovery_service.registry.register_agent(
            agent_id="generalist_1",
            agent_type="concierge",
            name="General Assistant",
            description="General purpose assistant",
            capabilities=[
                {"name": "data_analysis", "confidence_level": 0.7},
                {"name": "conversation", "confidence_level": 0.9}
            ]
        )
        
        # Create discovery request
        request = DiscoveryRequest(
            requesting_agent="test_requester",
            required_capabilities=["data_analysis"],
            strategy=DiscoveryStrategy.CAPABILITY_BASED,
            min_confidence=0.8
        )
        
        # Perform discovery
        results = await discovery_service.discover_agents(request)
        
        # Should find the specialist first (higher confidence)
        assert len(results) >= 1
        assert results[0].agent.agent_id == "specialist_1"
        assert results[0].match_score > 0.8

    @pytest.mark.asyncio
    async def test_hybrid_discovery(self, discovery_service):
        """Test hybrid discovery strategy."""
        # Register agents with different characteristics
        await discovery_service.registry.register_agent(
            agent_id="high_perf_agent",
            agent_type="analysis",
            name="High Performance Agent",
            description="High performance data agent",
            capabilities=[{"name": "data_processing", "confidence_level": 0.8}]
        )
        
        # Set performance metrics
        agent = discovery_service.registry.get_agent("high_perf_agent")
        agent.performance_metrics = {"accuracy": 0.95, "speed": 0.9}
        
        await discovery_service.registry.register_agent(
            agent_id="available_agent",
            agent_type="analysis",
            name="Available Agent",
            description="Highly available agent",
            capabilities=[{"name": "data_processing", "confidence_level": 0.8}]
        )
        
        # Set availability (low load)
        available_agent = discovery_service.registry.get_agent("available_agent")
        available_agent.current_load = 0
        
        # Create hybrid discovery request
        request = DiscoveryRequest(
            requesting_agent="test_requester",
            required_capabilities=["data_processing"],
            strategy=DiscoveryStrategy.HYBRID
        )
        
        results = await discovery_service.discover_agents(request)
        assert len(results) >= 2


class TestInterAgentMessaging:
    """Test the inter-agent messaging system."""

    @pytest.fixture
    def messaging_system(self):
        """Create messaging system for testing."""
        registry = NetworkAgentRegistry()
        return InterAgentMessagingSystem(registry)

    @pytest.mark.asyncio
    async def test_direct_messaging(self, messaging_system):
        """Test direct messaging between agents."""
        # Register test agents
        await messaging_system.registry.register_agent(
            agent_id="sender_agent",
            agent_type="test",
            name="Sender Agent",
            description="Test sender",
            capabilities=[{"name": "sending", "confidence_level": 0.8}]
        )
        
        await messaging_system.registry.register_agent(
            agent_id="receiver_agent",
            agent_type="test",
            name="Receiver Agent",
            description="Test receiver",
            capabilities=[{"name": "receiving", "confidence_level": 0.8}]
        )
        
        # Send a message
        message_id = await messaging_system.send_message(
            sender_agent="sender_agent",
            recipient_agent="receiver_agent",
            message_type=NetworkCommunicationType.DIRECT_MESSAGE,
            content={"text": "Hello, receiver!"},
            priority=NetworkMessagePriority.NORMAL
        )
        
        assert message_id is not None
        
        # Allow message processing
        await asyncio.sleep(0.2)
        
        # Check message was delivered
        messages = await messaging_system.get_messages_for_agent("receiver_agent")
        assert len(messages) >= 1
        assert messages[0].content["text"] == "Hello, receiver!"

    @pytest.mark.asyncio
    async def test_message_priorities(self, messaging_system):
        """Test message priority handling."""
        # Register agents
        await messaging_system.registry.register_agent(
            agent_id="priority_sender",
            agent_type="test",
            name="Priority Sender",
            description="Test priority sender",
            capabilities=[{"name": "priority_sending", "confidence_level": 0.8}]
        )
        
        await messaging_system.registry.register_agent(
            agent_id="priority_receiver",
            agent_type="test",
            name="Priority Receiver",
            description="Test priority receiver",
            capabilities=[{"name": "priority_receiving", "confidence_level": 0.8}]
        )
        
        # Send messages with different priorities
        low_msg_id = await messaging_system.send_message(
            sender_agent="priority_sender",
            recipient_agent="priority_receiver",
            message_type=NetworkCommunicationType.DIRECT_MESSAGE,
            content={"priority": "low"},
            priority=NetworkMessagePriority.LOW
        )
        
        high_msg_id = await messaging_system.send_message(
            sender_agent="priority_sender",
            recipient_agent="priority_receiver",
            message_type=NetworkCommunicationType.DIRECT_MESSAGE,
            content={"priority": "high"},
            priority=NetworkMessagePriority.HIGH
        )
        
        # Allow message processing
        await asyncio.sleep(0.2)
        
        # High priority message should be processed first
        messages = await messaging_system.get_messages_for_agent("priority_receiver")
        assert len(messages) >= 2
        
        # Messages should be ordered by priority (high first)
        high_priority_messages = [m for m in messages if m.content.get("priority") == "high"]
        assert len(high_priority_messages) >= 1


class TestNetworkWorkflowManager:
    """Test the network workflow manager."""

    @pytest.fixture
    def workflow_manager(self):
        """Create workflow manager for testing."""
        return NetworkWorkflowManager()

    @pytest.mark.asyncio
    async def test_sequential_workflow_creation(self, workflow_manager):
        """Test creating a sequential workflow."""
        # Define workflow tasks
        tasks = [
            {
                "name": "Task 1",
                "description": "First task in sequence",
                "required_capabilities": ["capability_1"],
                "dependencies": []
            },
            {
                "name": "Task 2",
                "description": "Second task in sequence",
                "required_capabilities": ["capability_2"],
                "dependencies": ["task_1"]
            }
        ]
        
        # Create workflow
        workflow_id = await workflow_manager.create_workflow(
            name="Test Sequential Workflow",
            description="Test workflow for sequential execution",
            pattern=WorkflowPattern.SEQUENTIAL,
            tasks=tasks,
            coordinator_agent="test_coordinator"
        )
        
        assert workflow_id is not None
        
        # Check workflow status
        status = workflow_manager.get_workflow_status(workflow_id)
        assert status is not None
        assert status["name"] == "Test Sequential Workflow"
        assert status["status"] == "initializing"
        assert len(status["tasks"]) == 2

    @pytest.mark.asyncio
    async def test_parallel_workflow_creation(self, workflow_manager):
        """Test creating a parallel workflow."""
        tasks = [
            {
                "name": "Parallel Task 1",
                "description": "First parallel task",
                "required_capabilities": ["parallel_capability_1"],
                "dependencies": []
            },
            {
                "name": "Parallel Task 2",
                "description": "Second parallel task",
                "required_capabilities": ["parallel_capability_2"],
                "dependencies": []
            }
        ]
        
        workflow_id = await workflow_manager.create_workflow(
            name="Test Parallel Workflow",
            description="Test workflow for parallel execution",
            pattern=WorkflowPattern.PARALLEL,
            tasks=tasks
        )
        
        assert workflow_id is not None
        
        status = workflow_manager.get_workflow_status(workflow_id)
        assert status["name"] == "Test Parallel Workflow"


class TestConsensusManager:
    """Test the consensus manager."""

    @pytest.fixture
    def consensus_manager(self):
        """Create consensus manager for testing."""
        return ConsensusManager()

    @pytest.mark.asyncio
    async def test_simple_majority_consensus(self, consensus_manager):
        """Test simple majority consensus mechanism."""
        # Create consensus process
        consensus_id = await consensus_manager.initiate_consensus(
            initiating_agent="test_initiator",
            title="Test Consensus",
            description="Testing simple majority consensus",
            consensus_type=ConsensusType.SIMPLE_MAJORITY,
            options=[
                {"id": "option_a", "name": "Option A"},
                {"id": "option_b", "name": "Option B"}
            ],
            participating_agents=["agent_1", "agent_2", "agent_3"]
        )
        
        assert consensus_id is not None
        
        # Submit votes
        await consensus_manager.submit_vote(
            consensus_id=consensus_id,
            voter_agent="agent_1",
            vote_data={"option": "option_a"}
        )
        
        await consensus_manager.submit_vote(
            consensus_id=consensus_id,
            voter_agent="agent_2",
            vote_data={"option": "option_a"}
        )
        
        await consensus_manager.submit_vote(
            consensus_id=consensus_id,
            voter_agent="agent_3",
            vote_data={"option": "option_b"}
        )
        
        # Allow consensus evaluation
        await asyncio.sleep(0.1)
        
        # Check consensus status
        status = consensus_manager.get_consensus_status(consensus_id)
        assert status is not None
        assert status["participation_rate"] == 1.0  # All agents voted
        assert status["votes_submitted"] == 3

    @pytest.mark.asyncio
    async def test_unanimous_consensus(self, consensus_manager):
        """Test unanimous consensus mechanism."""
        consensus_id = await consensus_manager.initiate_consensus(
            initiating_agent="test_initiator",
            title="Unanimous Test",
            description="Testing unanimous consensus",
            consensus_type=ConsensusType.UNANIMOUS,
            options=[
                {"id": "unanimous_option", "name": "The Only Option"}
            ],
            participating_agents=["agent_1", "agent_2"]
        )
        
        # All agents vote for the same option
        await consensus_manager.submit_vote(
            consensus_id=consensus_id,
            voter_agent="agent_1",
            vote_data={"option": "unanimous_option"}
        )
        
        await consensus_manager.submit_vote(
            consensus_id=consensus_id,
            voter_agent="agent_2",
            vote_data={"option": "unanimous_option"}
        )
        
        await asyncio.sleep(0.1)
        
        status = consensus_manager.get_consensus_status(consensus_id)
        assert status["participation_rate"] == 1.0


class TestNetworkIntelligence:
    """Test the network intelligence system."""

    @pytest.fixture
    def intelligence_system(self):
        """Create network intelligence system for testing."""
        return NetworkIntelligenceSystem()

    @pytest.mark.asyncio
    async def test_insight_creation_and_sharing(self, intelligence_system):
        """Test creating and sharing insights."""
        # Add an insight
        insight_id = await intelligence_system.add_insight(
            source_agent="test_agent",
            insight_type=InsightType.PATTERN_RECOGNITION,
            title="Test Pattern",
            description="A test pattern was detected",
            content={"pattern_type": "test", "confidence": 0.8},
            relevance_score=0.9,
            confidence_score=0.8,
            applicable_capabilities=["pattern_analysis"]
        )
        
        assert insight_id is not None
        
        # Retrieve insights
        insights = await intelligence_system.get_insights_for_agent(
            agent_id="test_agent",
            insight_types=[InsightType.PATTERN_RECOGNITION]
        )
        
        # Should find the insight we just added
        assert len(insights) >= 1
        found_insight = next((i for i in insights if i.insight_id == insight_id), None)
        assert found_insight is not None
        assert found_insight.title == "Test Pattern"

    @pytest.mark.asyncio
    async def test_performance_metric_recording(self, intelligence_system):
        """Test recording performance metrics."""
        # Record performance metrics
        await intelligence_system.record_performance_metric(
            agent_id="performance_test_agent",
            metric_name="accuracy",
            metric_value=0.95,
            context={"task_type": "data_analysis"}
        )
        
        await intelligence_system.record_performance_metric(
            agent_id="performance_test_agent",
            metric_name="speed",
            metric_value=0.8,
            context={"task_type": "data_analysis"}
        )
        
        # Check that metrics were recorded
        stats = intelligence_system.get_intelligence_stats()
        assert stats["performance_metrics"] >= 2

    @pytest.mark.asyncio
    async def test_interaction_pattern_recording(self, intelligence_system):
        """Test recording interaction patterns."""
        # Record successful interaction
        await intelligence_system.record_interaction_pattern(
            agent1_id="agent_a",
            agent2_id="agent_b",
            interaction_type="collaboration",
            outcome="success",
            metadata={"task_type": "data_analysis"}
        )
        
        # Record another successful interaction
        await intelligence_system.record_interaction_pattern(
            agent1_id="agent_a",
            agent2_id="agent_b",
            interaction_type="collaboration",
            outcome="success",
            metadata={"task_type": "visualization"}
        )
        
        # Check collaboration effectiveness
        effectiveness = intelligence_system.collaboration_effectiveness.get(("agent_a", "agent_b"))
        assert effectiveness is not None
        assert effectiveness > 0.5  # Should be high due to successful interactions


class TestNetworkIntegration:
    """Integration tests for the complete network architecture."""

    @pytest.mark.asyncio
    async def test_end_to_end_agent_communication(self):
        """Test complete agent communication flow."""
        # Create components
        registry = NetworkAgentRegistry()
        messaging = InterAgentMessagingSystem(registry)
        discovery = AgentDiscoveryService(registry)
        
        # Register agents
        await registry.register_agent(
            agent_id="integration_agent_1",
            agent_type="concierge",
            name="Integration Agent 1",
            description="First integration test agent",
            capabilities=[
                {"name": "conversation", "confidence_level": 0.9},
                {"name": "routing", "confidence_level": 0.8}
            ],
            communication_capabilities=["direct_message", "consultation"]
        )
        
        await registry.register_agent(
            agent_id="integration_agent_2",
            agent_type="analysis",
            name="Integration Agent 2",
            description="Second integration test agent",
            capabilities=[
                {"name": "data_analysis", "confidence_level": 0.9},
                {"name": "visualization", "confidence_level": 0.7}
            ],
            communication_capabilities=["direct_message", "consultation"]
        )
        
        # Agent 1 discovers Agent 2 for data analysis
        request = DiscoveryRequest(
            requesting_agent="integration_agent_1",
            required_capabilities=["data_analysis"],
            strategy=DiscoveryStrategy.CAPABILITY_BASED
        )
        
        results = await discovery.discover_agents(request)
        assert len(results) >= 1
        assert results[0].agent.agent_id == "integration_agent_2"
        
        # Agent 1 sends consultation request to Agent 2
        message_id = await messaging.send_message(
            sender_agent="integration_agent_1",
            recipient_agent="integration_agent_2",
            message_type=NetworkCommunicationType.CONSULTATION_REQUEST,
            content={
                "consultation_topic": "data_analysis_help",
                "context": {"data_type": "sales_data", "analysis_type": "trend_analysis"}
            },
            priority=NetworkMessagePriority.HIGH,
            response_expected=True
        )
        
        assert message_id is not None
        
        # Allow message processing
        await asyncio.sleep(0.2)
        
        # Agent 2 receives the consultation request
        messages = await messaging.get_messages_for_agent("integration_agent_2")
        assert len(messages) >= 1
        
        consultation_message = next(
            (m for m in messages if m.message_type == NetworkCommunicationType.CONSULTATION_REQUEST),
            None
        )
        assert consultation_message is not None
        assert consultation_message.content["consultation_topic"] == "data_analysis_help"
        
        # Agent 2 responds to the consultation
        response_id = await messaging.send_response(
            original_message=consultation_message,
            response_content={
                "consultation_response": "I can help with trend analysis",
                "recommendations": ["use_moving_averages", "seasonal_decomposition"]
            },
            response_type=NetworkCommunicationType.CONSULTATION_RESPONSE
        )
        
        assert response_id is not None
        
        # Allow response processing
        await asyncio.sleep(0.2)
        
        # Agent 1 receives the response
        response_messages = await messaging.get_messages_for_agent("integration_agent_1")
        response_message = next(
            (m for m in response_messages if m.message_type == NetworkCommunicationType.CONSULTATION_RESPONSE),
            None
        )
        
        assert response_message is not None
        assert "I can help with trend analysis" in response_message.content["consultation_response"]

    @pytest.mark.asyncio
    async def test_network_state_management(self):
        """Test network state creation and management."""
        # Create network state
        state = create_network_state(
            user_id="test_user",
            conversation_id="test_conversation",
            workflow_type="network_test",
            initial_message={"text": "Hello network!"},
            available_agents=[
                {
                    "agent_id": "state_agent_1",
                    "agent_type": "concierge",
                    "name": "State Agent 1",
                    "capabilities": ["conversation"],
                    "status": "active"
                }
            ]
        )
        
        assert state is not None
        assert state["user_id"] == "test_user"
        assert state["conversation_id"] == "test_conversation"
        assert state["workflow_type"] == "network_test"
        assert len(state["network_agents"]) == 1
        assert "state_agent_1" in state["network_agents"]
        
        # Test state updates
        assert state["network_messages"] == []
        assert state["pending_communications"] == {}
        assert state["active_workflows"] == {}


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
