"""
Extensible Strategy System for LangGraph-based Datagenius System.

This module provides a completely extensible, configuration-driven strategy system
that avoids hardcoded values and allows dynamic loading of persona behaviors.
"""

import logging
import importlib
import inspect
from typing import Dict, Any, List, Optional, Type, Callable
from pathlib import Path
import yaml
import json
from abc import ABC, abstractmethod
import uuid
from datetime import datetime

from ..states.unified_state import UnifiedDatageniusState, MessageType, add_message

logger = logging.getLogger(__name__)


class ExtensiblePersonaStrategy(ABC):
    """
    Base class for extensible persona strategies.
    
    All behavior is configuration-driven with no hardcoded values.
    Strategies are loaded dynamically based on configuration.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the extensible strategy."""
        self.config = config
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Load all configuration dynamically
        self.capabilities = config.get("capabilities", [])
        self.supported_intents = config.get("supported_intents", [])
        self.tools = config.get("tools", [])
        self.processing_rules = config.get("processing_rules", {})
        self.prompt_templates = config.get("prompt_templates", {})
        self.methodology_config = config.get("methodology", {})
        self.custom_handlers = config.get("custom_handlers", {})
        
        # Load custom processing functions if specified
        self.custom_processors = self._load_custom_processors()
        
    def _load_custom_processors(self) -> Dict[str, Callable]:
        """Load custom processing functions from configuration."""
        processors = {}
        
        for processor_name, processor_config in self.custom_handlers.items():
            try:
                module_path = processor_config.get("module")
                function_name = processor_config.get("function")
                
                if module_path and function_name:
                    module = importlib.import_module(module_path)
                    processor_func = getattr(module, function_name)
                    processors[processor_name] = processor_func
                    
            except Exception as e:
                self.logger.error(f"Error loading custom processor {processor_name}: {e}")
        
        return processors
    
    @abstractmethod
    async def process_message(
        self, 
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process message using configuration-driven logic."""
        pass
    
    def get_system_prompt(self, context: Dict[str, Any]) -> str:
        """Get system prompt from configuration templates."""
        # Use template from configuration
        template_name = self.prompt_templates.get("system_prompt", "default")
        
        if template_name in self.prompt_templates:
            template = self.prompt_templates[template_name]
            
            # Replace placeholders with context values
            return self._render_template(template, context)
        
        # Fallback to basic prompt
        return self.config.get("default_prompt", "You are a helpful AI assistant.")
    
    def _render_template(self, template: str, context: Dict[str, Any]) -> str:
        """Render template with context values."""
        try:
            # Simple template rendering - can be extended with Jinja2 if needed
            rendered = template
            
            for key, value in context.items():
                placeholder = f"{{{key}}}"
                if placeholder in rendered:
                    rendered = rendered.replace(placeholder, str(value))
            
            return rendered
            
        except Exception as e:
            self.logger.error(f"Error rendering template: {e}")
            return template
    
    def get_specialized_tools(self) -> List[str]:
        """Get specialized tools from configuration."""
        return self.tools
    
    def can_handle_intent(self, intent: str) -> bool:
        """Check if strategy can handle intent based on configuration."""
        return intent in self.supported_intents
    
    def get_capability_score(self, request_context: Dict[str, Any]) -> float:
        """Calculate capability score using configuration-driven rules."""
        scoring_rules = self.processing_rules.get("capability_scoring", {})
        
        # Default scoring based on intent matching
        intent = request_context.get("detected_intent", "")
        base_score = 0.8 if self.can_handle_intent(intent) else 0.1
        
        # Apply custom scoring rules if configured
        for rule_name, rule_config in scoring_rules.items():
            try:
                score_modifier = self._apply_scoring_rule(rule_config, request_context)
                base_score = min(1.0, base_score + score_modifier)
            except Exception as e:
                self.logger.error(f"Error applying scoring rule {rule_name}: {e}")
        
        return base_score
    
    def _apply_scoring_rule(self, rule_config: Dict[str, Any], context: Dict[str, Any]) -> float:
        """Apply a single scoring rule."""
        rule_type = rule_config.get("type", "keyword_match")
        
        if rule_type == "keyword_match":
            keywords = rule_config.get("keywords", [])
            message = context.get("message", "").lower()
            matches = sum(1 for keyword in keywords if keyword.lower() in message)
            return matches * rule_config.get("score_per_match", 0.1)
        
        elif rule_type == "capability_match":
            capabilities = rule_config.get("capabilities", [])
            request_capabilities = context.get("required_capabilities", [])
            matches = len(set(capabilities) & set(request_capabilities))
            return matches * rule_config.get("score_per_match", 0.2)
        
        return 0.0


class ConfigurablePersonaStrategy(ExtensiblePersonaStrategy):
    """
    Fully configurable persona strategy that implements behavior
    entirely through configuration without hardcoded logic.
    """

    def __init__(self, config: Dict[str, Any]):
        """Initialize the configurable strategy with LLM integration."""
        super().__init__(config)

        # Initialize LLM processor for response generation
        self.llm_processor = None
        self._initialize_llm_processor()

    def _initialize_llm_processor(self):
        """Initialize LLM processor for response generation."""
        try:
            # Import LLM processor
            from ...components.shared.shared_llm_processor import SharedLLMProcessor

            # Initialize with configuration
            llm_config = self.config.get("llm_config", {})
            self.llm_processor = SharedLLMProcessor(llm_config)

            self.logger.info("LLM processor initialized for configurable strategy")

        except Exception as e:
            self.logger.error(f"Error initializing LLM processor: {e}")
            self.llm_processor = None

    async def process_message(
        self,
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Process message using configuration-driven workflow with response generation."""
        self.logger.info(f"Processing with configurable strategy: {self.config.get('name', 'unknown')}")

        try:
            # Apply processing pipeline from configuration
            pipeline = self.processing_rules.get("processing_pipeline", [])

            for step in pipeline:
                try:
                    state = await self._execute_processing_step(step, state, context)
                except Exception as e:
                    self.logger.error(f"Error in processing step {step.get('name', 'unknown')}: {e}")

            # Generate response using LLM
            state = await self._generate_response(state, context)

            return state

        except Exception as e:
            self.logger.error(f"Error in configurable strategy processing: {e}")

            # Generate fallback response
            fallback_message = {
                "id": str(uuid.uuid4()),
                "content": "I apologize, but I encountered an error while processing your request. Please try again.",
                "role": "assistant",
                "timestamp": datetime.now().isoformat(),
                "type": "error_response",
                "agent_id": self.config.get("id", "unknown"),
                "in_response_to": state.get("current_message_id")  # Add this field for workflow manager
            }

            return add_message(state, fallback_message, MessageType.AGENT)

    async def _generate_response(
        self,
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Generate response using LLM integration."""
        try:
            # Get system prompt from configuration
            system_prompt = self.get_system_prompt(context)

            # Get user message
            user_message = self._extract_user_message(state)

            # Prepare context for LLM
            llm_context = self._prepare_llm_context(state, context)

            # Build full prompt
            full_prompt = self._build_prompt(system_prompt, user_message, llm_context)

            # Generate response using LLM processor
            if self.llm_processor:
                response_text = await self._call_llm(full_prompt, context)
            else:
                # Fallback to template-based response
                response_text = self._generate_template_response(context)

            # Create response message
            response_message = {
                "id": str(uuid.uuid4()),
                "content": response_text,
                "role": "assistant",
                "timestamp": datetime.now().isoformat(),
                "type": "agent_response",
                "agent_id": self.config.get("id", "unknown"),
                "persona_type": self.config.get("agent_type", "unknown"),
                "in_response_to": state.get("current_message_id")  # Add this field for workflow manager
            }

            # Add response to state
            state = add_message(state, response_message, MessageType.AGENT)

            # Update workflow context
            state.workflow_context["response_generated"] = True
            state.workflow_context["last_response_timestamp"] = datetime.now().isoformat()

            return state

        except Exception as e:
            self.logger.error(f"Error generating response: {e}")

            # Generate error response
            error_message = {
                "id": str(uuid.uuid4()),
                "content": "I apologize, but I'm having trouble generating a response right now. Please try again.",
                "role": "assistant",
                "timestamp": datetime.now().isoformat(),
                "type": "error_response",
                "agent_id": self.config.get("id", "unknown"),
                "in_response_to": state.get("current_message_id")  # Add this field for workflow manager
            }

            return add_message(state, error_message, MessageType.AGENT)

    def _extract_user_message(self, state: UnifiedDatageniusState) -> str:
        """Extract the latest user message from state."""
        try:
            # Get the most recent user message
            user_messages = [
                msg for msg in state["messages"]
                if msg.get("role") == "user" or msg.get("type") == "user"
            ]

            if user_messages:
                return user_messages[-1].get("content", "")

            # Fallback to current message
            current_message = state.get("current_message", {})
            return current_message.get("content", "")

        except Exception as e:
            self.logger.error(f"Error extracting user message: {e}")
            return ""

    def _prepare_llm_context(
        self,
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Prepare context for LLM processing."""
        llm_context = {
            "user_id": state.get("user_id"),
            "conversation_id": state.get("conversation_id"),
            "business_profile": state.get("business_context", {}),
            "workflow_context": state.get("workflow_context", {}),
            "agent_type": self.config.get("agent_type", "unknown"),
            "capabilities": self.capabilities,
            "conversation_history": self._get_conversation_history(state),
            "cross_agent_insights": self._get_relevant_insights(state)
        }

        # Add configuration-specific context
        llm_context.update(context)

        return llm_context

    def _get_conversation_history(self, state: UnifiedDatageniusState) -> List[Dict[str, Any]]:
        """Get relevant conversation history for context."""
        try:
            # Get last 10 messages for context
            messages = state.get("messages", [])
            return messages[-10:] if len(messages) > 10 else messages

        except Exception as e:
            self.logger.error(f"Error getting conversation history: {e}")
            return []

    def _get_relevant_insights(self, state: UnifiedDatageniusState) -> List[Dict[str, Any]]:
        """Get relevant cross-agent insights for context."""
        try:
            # Get recent insights relevant to this agent type
            agent_type = self.config.get("agent_type", "unknown")
            insights = state.get("shared_insights", [])

            # Filter for relevant insights
            relevant_insights = [
                insight for insight in insights[-20:]  # Last 20 insights
                if (
                    insight.get("target_agents", []) == [] or  # General insights
                    agent_type in insight.get("target_agents", []) or  # Targeted insights
                    insight.get("insight_type") in ["business_context_update", "general"]
                )
            ]

            return relevant_insights

        except Exception as e:
            self.logger.error(f"Error getting relevant insights: {e}")
            return []

    def _build_prompt(
        self,
        system_prompt: str,
        user_message: str,
        context: Dict[str, Any]
    ) -> str:
        """Build the complete prompt for LLM."""
        try:
            # Get prompt template from configuration
            prompt_template = self.config.get("prompt_template", {})

            if prompt_template:
                # Use configured template
                template = prompt_template.get("template", "{system_prompt}\n\nUser: {user_message}\n\nAssistant:")

                # Render template with context
                return self._render_template(template, {
                    "system_prompt": system_prompt,
                    "user_message": user_message,
                    **context
                })
            else:
                # Default prompt structure
                context_str = ""
                if context.get("business_profile"):
                    context_str += f"\nBusiness Context: {context['business_profile']}"

                if context.get("conversation_history"):
                    context_str += f"\nConversation History: {context['conversation_history'][-3:]}"  # Last 3 messages

                return f"{system_prompt}{context_str}\n\nUser: {user_message}\n\nAssistant:"

        except Exception as e:
            self.logger.error(f"Error building prompt: {e}")
            return f"{system_prompt}\n\nUser: {user_message}\n\nAssistant:"

    async def _call_llm(self, prompt: str, context: Dict[str, Any]) -> str:
        """Call LLM to generate response."""
        try:
            if not self.llm_processor:
                raise ValueError("LLM processor not initialized")

            # Prepare LLM request context
            from ...components.shared.shared_llm_processor import LLMRequestContext

            llm_context = LLMRequestContext(
                user_id=context.get("user_id"),
                conversation_id=context.get("conversation_id"),
                agent_identity=self.config.get("name", "AI Assistant"),
                business_profile_id=context.get("business_profile_id")
            )

            # Generate response
            result_context = await self.llm_processor.generate_response(
                prompt=prompt,
                context=llm_context,
                preferred_model=self.config.get("preferred_model"),
                temperature=self.config.get("temperature", 0.7),
                max_tokens=self.config.get("max_tokens", 4000)
            )

            if result_context.status == "success":
                return result_context.get_field("llm_response", "I apologize, but I couldn't generate a response.")
            else:
                self.logger.warning(f"LLM request failed: {result_context.error}")
                return self._generate_template_response(context)

        except Exception as e:
            self.logger.error(f"Error calling LLM: {e}")
            return self._generate_template_response(context)

    def _generate_template_response(self, context: Dict[str, Any]) -> str:
        """Generate fallback response using templates."""
        try:
            # Get response templates from configuration
            response_templates = self.config.get("response_templates", {})

            # Select appropriate template based on context
            template_key = "default"
            if context.get("intent"):
                template_key = context["intent"]
            elif self.config.get("agent_type"):
                template_key = self.config["agent_type"]

            template = response_templates.get(template_key, response_templates.get("default",
                "I'm here to help you with {capabilities}. How can I assist you today?"))

            # Render template with context
            return self._render_template(template, {
                "capabilities": ", ".join(self.capabilities),
                "agent_name": self.config.get("name", "AI Assistant"),
                **context
            })

        except Exception as e:
            self.logger.error(f"Error generating template response: {e}")
            return "I'm here to help! How can I assist you today?"

    async def _execute_processing_step(
        self,
        step_config: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Execute a single processing step from configuration."""
        step_type = step_config.get("type")
        step_name = step_config.get("name", "unknown")
        
        if step_type == "context_extraction":
            # Extract context based on configuration
            extraction_rules = step_config.get("extraction_rules", {})
            for key, rule in extraction_rules.items():
                extracted_value = self._extract_context_value(rule, context, state)
                state.workflow_context[key] = extracted_value
        
        elif step_type == "methodology_application":
            # Apply methodology framework from configuration
            methodology = step_config.get("methodology", {})
            state = await self._apply_methodology(methodology, state, context)
        
        elif step_type == "custom_processor":
            # Execute custom processor function
            processor_name = step_config.get("processor")
            if processor_name in self.custom_processors:
                processor_func = self.custom_processors[processor_name]
                state = await processor_func(state, context, step_config)
        
        elif step_type == "state_update":
            # Update state based on configuration
            updates = step_config.get("updates", {})
            for key, value in updates.items():
                if isinstance(value, str) and value.startswith("${"):
                    # Dynamic value from context
                    context_key = value[2:-1]  # Remove ${ and }
                    value = context.get(context_key, value)
                state.workflow_context[key] = value

        elif step_type == "response_generation":
            # Generate response using LLM (this step is handled in main process_message)
            # This step type is for configuration completeness
            state.workflow_context["response_generation_requested"] = True

        elif step_type == "strategy_application":
            # Apply strategy-specific logic from configuration
            strategy_config = step_config.get("strategy", {})
            state = await self._apply_strategy_logic(strategy_config, state, context)

        elif step_type == "business_profile_lookup":
            # Extract business profile information
            lookup_key = step_config.get("key")
            default_value = step_config.get("default")

            business_context = state.get("business_context", {})
            profile_data = business_context.get("profile_data", {})

            if lookup_key and lookup_key in profile_data:
                state.workflow_context[f"business_{lookup_key}"] = profile_data[lookup_key]
            elif default_value is not None:
                state.workflow_context[f"business_{lookup_key}"] = default_value

        return state

    async def _apply_strategy_logic(
        self,
        strategy_config: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Apply strategy-specific logic from configuration."""
        try:
            strategy_type = strategy_config.get("type")

            if strategy_type == "content_focused":
                # Apply content-focused strategy
                approach = strategy_config.get("approach", "general")
                personalization_level = strategy_config.get("personalization_level", "medium")

                state.workflow_context["content_strategy"] = {
                    "approach": approach,
                    "personalization_level": personalization_level,
                    "focus": "content_creation"
                }

            elif strategy_type == "data_analysis":
                # Apply data analysis strategy
                methodology = strategy_config.get("methodology", "standard")
                depth_level = strategy_config.get("depth_level", "medium")

                state.workflow_context["analysis_strategy"] = {
                    "methodology": methodology,
                    "depth_level": depth_level,
                    "focus": "data_insights"
                }

            elif strategy_type == "adaptive_classification":
                # Apply adaptive classification strategy
                methods = strategy_config.get("methods", ["llm_classification"])
                selection_criteria = strategy_config.get("selection_criteria", [])

                state.workflow_context["classification_strategy"] = {
                    "methods": methods,
                    "selection_criteria": selection_criteria,
                    "focus": "content_classification"
                }

            return state

        except Exception as e:
            self.logger.error(f"Error applying strategy logic: {e}")
            return state
    
    def _extract_context_value(
        self,
        rule: Dict[str, Any],
        context: Dict[str, Any],
        state: UnifiedDatageniusState
    ) -> Any:
        """Extract context value based on extraction rule."""
        rule_type = rule.get("type", "direct")

        if rule_type == "direct":
            return context.get(rule.get("key"), rule.get("default"))

        elif rule_type == "state_lookup":
            return state.workflow_context.get(rule.get("key"), rule.get("default"))

        elif rule_type == "business_profile_lookup":
            # Extract from business profile data
            business_context = state.get("business_context", {})
            profile_data = business_context.get("profile_data", {})
            return profile_data.get(rule.get("key"), rule.get("default"))

        elif rule_type == "computed":
            # Simple computed values - can be extended
            computation = rule.get("computation", {})
            if computation.get("type") == "count":
                items = context.get(computation.get("items_key"), [])
                return len(items)

        return rule.get("default")
    
    async def _apply_methodology(
        self,
        methodology: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Apply methodology framework from configuration."""
        framework_type = methodology.get("type")
        
        if framework_type == "stage_based":
            stages = methodology.get("stages", [])
            current_stage = state.workflow_context.get("current_stage", stages[0] if stages else "default")
            
            # Find current stage configuration
            stage_config = None
            for stage in stages:
                if stage.get("name") == current_stage:
                    stage_config = stage
                    break
            
            if stage_config:
                # Apply stage-specific processing
                stage_actions = stage_config.get("actions", [])
                for action in stage_actions:
                    state = await self._execute_stage_action(action, state, context)
                
                # Determine next stage
                next_stage = stage_config.get("next_stage")
                if next_stage:
                    state.workflow_context["current_stage"] = next_stage
        
        return state
    
    async def _execute_stage_action(
        self,
        action: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> UnifiedDatageniusState:
        """Execute a stage action from methodology configuration."""
        action_type = action.get("type")
        
        if action_type == "set_context":
            key = action.get("key")
            value = action.get("value")
            if key and value is not None:
                state.workflow_context[key] = value
        
        elif action_type == "validate_condition":
            condition = action.get("condition", {})
            if self._evaluate_condition(condition, state, context):
                state.workflow_context[action.get("result_key", "condition_met")] = True
        
        return state
    
    def _evaluate_condition(
        self,
        condition: Dict[str, Any],
        state: UnifiedDatageniusState,
        context: Dict[str, Any]
    ) -> bool:
        """Evaluate a condition from configuration."""
        condition_type = condition.get("type", "exists")
        
        if condition_type == "exists":
            key = condition.get("key")
            source = condition.get("source", "context")
            
            if source == "context":
                return key in context
            elif source == "state":
                return key in state.workflow_context
        
        elif condition_type == "equals":
            key = condition.get("key")
            expected_value = condition.get("value")
            source = condition.get("source", "context")
            
            if source == "context":
                return context.get(key) == expected_value
            elif source == "state":
                return state.workflow_context.get(key) == expected_value
        
        return False


class ExtensibleStrategyRegistry:
    """
    Registry for managing extensible persona strategies.
    
    Loads strategies dynamically from configuration files and modules.
    """
    
    def __init__(self, config_dir: Optional[Path] = None):
        """Initialize the extensible strategy registry."""
        self.logger = logging.getLogger(__name__)
        
        # Configuration directory
        self.config_dir = config_dir or Path(__file__).parent.parent / "config" / "strategies"
        
        # Strategy configurations
        self.strategy_configs: Dict[str, Dict[str, Any]] = {}
        
        # Strategy classes cache
        self.strategy_classes: Dict[str, Type[ExtensiblePersonaStrategy]] = {}
        
        # Strategy instances cache
        self.strategy_instances: Dict[str, ExtensiblePersonaStrategy] = {}
        
        # Load configurations
        self._load_strategy_configurations()
        
        self.logger.info("ExtensibleStrategyRegistry initialized")
    
    def _load_strategy_configurations(self) -> None:
        """Load strategy configurations from files."""
        if not self.config_dir.exists():
            self.logger.warning(f"Strategy config directory not found: {self.config_dir}")
            return
        
        # Load YAML configuration files
        for config_file in self.config_dir.glob("*.yaml"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                
                strategy_id = config_file.stem
                self.strategy_configs[strategy_id] = config
                
                self.logger.info(f"Loaded strategy configuration: {strategy_id}")
                
            except Exception as e:
                self.logger.error(f"Error loading strategy config {config_file}: {e}")
        
        # Load JSON configuration files
        for config_file in self.config_dir.glob("*.json"):
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                strategy_id = config_file.stem
                self.strategy_configs[strategy_id] = config
                
                self.logger.info(f"Loaded strategy configuration: {strategy_id}")
                
            except Exception as e:
                self.logger.error(f"Error loading strategy config {config_file}: {e}")
    
    def get_strategy(
        self,
        strategy_id: str,
        config_override: Optional[Dict[str, Any]] = None
    ) -> ExtensiblePersonaStrategy:
        """
        Get a strategy instance by ID.
        
        Args:
            strategy_id: Strategy identifier
            config_override: Optional configuration override
            
        Returns:
            ExtensiblePersonaStrategy instance
        """
        # Check cache
        cache_key = f"{strategy_id}_{hash(str(config_override)) if config_override else 'default'}"
        if cache_key in self.strategy_instances:
            return self.strategy_instances[cache_key]
        
        # Get configuration
        if strategy_id not in self.strategy_configs:
            self.logger.warning(f"Strategy {strategy_id} not found, using configurable strategy")
            config = config_override or {"name": strategy_id}
        else:
            config = self.strategy_configs[strategy_id].copy()
            if config_override:
                config.update(config_override)
        
        # Get strategy class
        strategy_class = self._get_strategy_class(config)
        
        # Create instance
        strategy_instance = strategy_class(config)
        
        # Cache instance
        self.strategy_instances[cache_key] = strategy_instance
        
        return strategy_instance
    
    def _get_strategy_class(self, config: Dict[str, Any]) -> Type[ExtensiblePersonaStrategy]:
        """Get strategy class from configuration."""
        # Check if custom class is specified
        custom_class = config.get("strategy_class")
        if custom_class:
            try:
                module_path, class_name = custom_class.rsplit(".", 1)
                module = importlib.import_module(module_path)
                strategy_class = getattr(module, class_name)
                
                if issubclass(strategy_class, ExtensiblePersonaStrategy):
                    return strategy_class
                    
            except Exception as e:
                self.logger.error(f"Error loading custom strategy class {custom_class}: {e}")
        
        # Use default configurable strategy
        return ConfigurablePersonaStrategy
    
    def register_strategy_config(self, strategy_id: str, config: Dict[str, Any]) -> None:
        """Register a strategy configuration programmatically."""
        self.strategy_configs[strategy_id] = config
        
        # Clear cache for this strategy
        keys_to_remove = [key for key in self.strategy_instances.keys() if key.startswith(f"{strategy_id}_")]
        for key in keys_to_remove:
            del self.strategy_instances[key]
        
        self.logger.info(f"Registered strategy configuration: {strategy_id}")
    
    def list_strategies(self) -> List[str]:
        """List all available strategy IDs."""
        return list(self.strategy_configs.keys())
    
    def reload_configurations(self) -> None:
        """Reload all strategy configurations."""
        self.strategy_configs.clear()
        self.strategy_instances.clear()
        self._load_strategy_configurations()
        self.logger.info("Reloaded all strategy configurations")


# Global registry instance
extensible_strategy_registry = ExtensibleStrategyRegistry()
