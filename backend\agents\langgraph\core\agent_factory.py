"""
Agent Node Factory for LangGraph-based Datagenius System.

This module provides a factory system for dynamically creating and
configuring agent nodes, replacing the existing agent registry with
a LangGraph-native approach.
"""

import logging
from typing import Dict, Any, List, Optional, Type, Union
from datetime import datetime
import importlib
import inspect
from pathlib import Path
import yaml

from ..nodes.base_agent_node import BaseAgentNode, UnifiedAgentNode

logger = logging.getLogger(__name__)


class AgentNodeFactory:
    """
    Factory for creating and managing agent nodes in the LangGraph system.
    
    This factory replaces the existing agent registry with a system
    optimized for LangGraph workflows and dynamic agent creation.
    """
    
    def __init__(self):
        """Initialize the agent node factory."""
        self.logger = logging.getLogger(__name__)
        
        # Registry of agent configurations
        self.agent_configs: Dict[str, Dict[str, Any]] = {}

        # Cache of created agent instances
        self.agent_instances: Dict[str, BaseAgentNode] = {}

        # Agent metadata
        self.agent_metadata: Dict[str, Dict[str, Any]] = {}

        # Backward compatibility: maintain agent_classes for legacy code
        self.agent_classes: Dict[str, Type[BaseAgentNode]] = {}
        
        # Initialize with built-in agents
        self._register_builtin_agents()
        
        self.logger.info("AgentNodeFactory initialized")
    
    def _register_builtin_agents(self) -> None:
        """Load agent configurations from persona YAML files."""
        try:
            # Load from individual persona YAML files (unified approach)
            personas_dir = Path(__file__).parent.parent / "config" / "personas"
            if personas_dir.exists():
                loaded_count = 0

                for yaml_file in personas_dir.glob("*.yaml"):
                    try:
                        with open(yaml_file, 'r', encoding='utf-8') as f:
                            config = yaml.safe_load(f)

                        if config:
                            # Use 'id' field as the primary identifier
                            agent_id = config.get("id")
                            if agent_id:
                                self.register_agent_config(agent_id, config)
                                loaded_count += 1
                                self.logger.debug(f"Loaded persona: {agent_id} from {yaml_file.name}")
                            else:
                                self.logger.warning(f"No 'id' field in {yaml_file.name}")
                        else:
                            self.logger.warning(f"Empty config in {yaml_file.name}")

                    except Exception as e:
                        self.logger.error(f"Error loading {yaml_file.name}: {e}")

                self.logger.info(f"Loaded {loaded_count} personas from YAML files")

                if loaded_count == 0:
                    self.logger.warning("No personas loaded, falling back to default agents")
                    self._register_default_agents()
            else:
                self.logger.warning(f"Personas directory not found: {personas_dir}")
                self._register_default_agents()

        except Exception as e:
            self.logger.error(f"Error loading persona configurations: {e}")
            self._register_default_agents()

    def _register_default_agents(self) -> None:
        """Register default agents as fallback."""
        default_agents = {
            "concierge": {
                "agent_type": "concierge",
                "name": "Datagenius Concierge",
                "agent_class": "agents.langgraph.agents.concierge_agent.UserSelectedConciergeAgent",
                "agent_init_config": {
                    "config": {
                        "agent_id": "concierge"
                    }
                },
                "capabilities": ["persona_recommendation", "intent_analysis", "conversation_management"],
                "supported_intents": ["greeting", "general_inquiry", "persona_request"],
                "fallback": True
            }
        }

        for agent_id, config in default_agents.items():
            self.register_agent_config(agent_id, config)

    def register_agent_config(
        self,
        agent_id: str,
        config: Dict[str, Any]
    ) -> None:
        """
        Register an agent configuration.

        Args:
            agent_id: Unique identifier for the agent
            config: Agent configuration dictionary
        """
        self.agent_configs[agent_id] = config

        # Extract metadata from config
        metadata = {
            "name": config.get("name", agent_id),
            "description": config.get("description", ""),
            "agent_type": config.get("agent_type", "general"),
            "capabilities": config.get("capabilities", []),
            "supported_intents": config.get("supported_intents", []),
            "tools": config.get("tools", []),
            "priority": config.get("priority", 3),
            "fallback": config.get("fallback", False)
        }

        self.agent_metadata[agent_id] = metadata

        self.logger.info(f"Registered agent config: {agent_id}")

    def register_agent_class(
        self,
        agent_id: str,
        agent_class: Type[BaseAgentNode],
        config: Optional[Dict[str, Any]] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Register an agent node class (backward compatibility method).

        This method converts the old class-based registration to the new
        configuration-based system.

        Args:
            agent_id: Unique identifier for the agent
            agent_class: Agent node class
            config: Optional default configuration
            metadata: Optional agent metadata
        """
        # Store in agent_classes for backward compatibility
        self.agent_classes[agent_id] = agent_class

        # Convert to configuration format
        agent_config = config or {}
        agent_config.update({
            "agent_class": f"{agent_class.__module__}.{agent_class.__name__}",
            "agent_type": self._derive_agent_type_from_class(agent_class),
            "name": getattr(agent_class, "name", agent_id.replace("-", " ").title()),
            "description": (agent_class.__doc__ or "").strip().split('\n')[0] if agent_class.__doc__ else ""
        })

        # Extract metadata from class if not provided
        if metadata is None:
            metadata = self._extract_agent_metadata(agent_class)

        # Add capabilities, intents, tools from class attributes
        for attr in ["capabilities", "supported_intents", "tools"]:
            if hasattr(agent_class, attr):
                agent_config[attr] = getattr(agent_class, attr, [])

        # Register using the new system
        self.register_agent_config(agent_id, agent_config)

        self.logger.info(f"Registered agent class (legacy): {agent_id}")

    def _derive_agent_type_from_class(self, agent_class: Type[BaseAgentNode]) -> str:
        """Derive agent type from class name."""
        class_name = agent_class.__name__.lower()
        if "concierge" in class_name:
            return "concierge"
        elif "marketing" in class_name:
            return "marketing"
        elif "analysis" in class_name:
            return "analysis"
        elif "classification" in class_name or "classifier" in class_name:
            return "classification"
        else:
            return "general"
    
    def _extract_agent_metadata(self, agent_class: Type[BaseAgentNode]) -> Dict[str, Any]:
        """
        Extract metadata from an agent class.
        
        Args:
            agent_class: Agent class to analyze
            
        Returns:
            Extracted metadata
        """
        metadata = {
            "class_name": agent_class.__name__,
            "module": agent_class.__module__,
            "capabilities": [],
            "supported_intents": [],
            "tools": [],
            "description": "",
            "registered_at": datetime.now().isoformat()
        }
        
        # Try to get capabilities from class attributes
        if hasattr(agent_class, "capabilities"):
            metadata["capabilities"] = getattr(agent_class, "capabilities", [])
        
        if hasattr(agent_class, "supported_intents"):
            metadata["supported_intents"] = getattr(agent_class, "supported_intents", [])
        
        if hasattr(agent_class, "tools"):
            metadata["tools"] = getattr(agent_class, "tools", [])
        
        # Get description from docstring
        if agent_class.__doc__:
            metadata["description"] = agent_class.__doc__.strip().split('\n')[0]
        
        return metadata
    
    def create_agent_node(
        self,
        agent_id: str,
        config: Optional[Dict[str, Any]] = None,
        force_new: bool = False
    ) -> Optional[BaseAgentNode]:
        """
        Create an agent node instance.
        
        Args:
            agent_id: Agent identifier
            config: Optional configuration override
            force_new: Force creation of new instance
            
        Returns:
            Agent node instance or None if not found
        """
        try:
            # Check if agent config is registered
            if agent_id not in self.agent_configs:
                self.logger.error(f"Agent config not registered: {agent_id}")
                return None

            # Return cached instance if available and not forcing new
            if not force_new and agent_id in self.agent_instances:
                self.logger.debug(f"Returning cached agent instance: {agent_id}")
                return self.agent_instances[agent_id]

            # Merge configurations
            base_config = self.agent_configs.get(agent_id, {})
            final_config = {**base_config, **(config or {})}

            # Create new UnifiedAgentNode instance
            agent_instance = UnifiedAgentNode(agent_id, final_config)

            # Cache the instance
            self.agent_instances[agent_id] = agent_instance

            self.logger.info(f"Created agent node instance: {agent_id}")

            return agent_instance

        except Exception as e:
            self.logger.error(f"Error creating agent node {agent_id}: {e}", exc_info=True)
            return None

    async def create_unified_persona_node(
        self,
        agent_id: str,
        config: Optional[Dict[str, Any]] = None,
        business_profile: Optional[Dict[str, Any]] = None
    ) -> "UnifiedPersonaNode":
        """
        Create a unified persona node for the given agent ID.

        Args:
            agent_id: Agent identifier
            config: Optional configuration override
            business_profile: Optional business profile context

        Returns:
            UnifiedPersonaNode instance
        """
        from ..nodes.unified_persona_node import create_unified_persona_node

        # Get agent configuration
        if agent_id not in self.agent_configs:
            self.logger.warning(f"Agent {agent_id} not found in registry, using default")
            agent_config = {
                "persona_id": agent_id,
                "agent_type": "default",
                "name": agent_id.replace("-", " ").title(),
                "description": "Default AI assistant"
            }
        else:
            agent_config = self.agent_configs[agent_id].copy()

        # Apply configuration override
        if config:
            agent_config.update(config)

        # Create unified persona node
        unified_node = create_unified_persona_node(agent_config, business_profile)

        self.logger.info(f"Created unified persona node: {agent_id}")
        return unified_node

    def get_persona_types(self) -> List[str]:
        """
        Get all available persona types.

        Returns:
            List of persona type identifiers
        """
        persona_types = set()
        for config in self.agent_configs.values():
            agent_type = config.get("agent_type", "default")
            persona_types.add(agent_type)

        return sorted(list(persona_types))

    def get_agents_by_persona_type(self, persona_type: str) -> List[str]:
        """
        Get all agent IDs for a specific persona type.

        Args:
            persona_type: Persona type identifier

        Returns:
            List of agent IDs
        """
        matching_agents = []
        for agent_id, config in self.agent_configs.items():
            if config.get("agent_type") == persona_type:
                matching_agents.append(agent_id)

        return matching_agents

    def validate_agent_config(self, agent_id: str) -> Dict[str, Any]:
        """
        Validate an agent configuration.

        Args:
            agent_id: Agent identifier

        Returns:
            Validation results
        """
        if agent_id not in self.agent_configs:
            return {
                "valid": False,
                "agent_id": agent_id,
                "errors": ["Agent not found in registry"]
            }

        config = self.agent_configs[agent_id]
        errors = []

        # Check required fields
        required_fields = ["agent_type", "name"]
        for field in required_fields:
            if field not in config:
                errors.append(f"Missing required field: {field}")

        # Validate agent type
        valid_types = ["analysis", "marketing", "concierge", "classification", "default"]
        agent_type = config.get("agent_type")
        if agent_type and agent_type not in valid_types:
            errors.append(f"Invalid agent type: {agent_type}")

        return {
            "valid": len(errors) == 0,
            "agent_id": agent_id,
            "errors": errors,
            "config": config
        }
    
    def get_agent_node(self, agent_id: str) -> Optional[BaseAgentNode]:
        """
        Get an existing agent node instance.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            Agent node instance or None
        """
        return self.agent_instances.get(agent_id)
    
    def create_all_agents(
        self,
        global_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, BaseAgentNode]:
        """
        Create instances of all registered agents.
        
        Args:
            global_config: Optional global configuration
            
        Returns:
            Dictionary of agent instances
        """
        agents = {}
        
        for agent_id in self.agent_configs.keys():
            agent_config = global_config.get(agent_id, {}) if global_config else {}
            agent_instance = self.create_agent_node(agent_id, agent_config)

            if agent_instance:
                agents[agent_id] = agent_instance
        
        self.logger.info(f"Created {len(agents)} agent instances")
        
        return agents
    
    def get_available_agents(self) -> List[str]:
        """Get list of available agent IDs."""
        return list(self.agent_configs.keys())

    def get_agent_config(self, agent_id: str) -> Optional[Dict[str, Any]]:
        """
        Get configuration for a specific agent.

        Args:
            agent_id: Agent identifier

        Returns:
            Agent configuration dictionary or None if not found
        """
        return self.agent_configs.get(agent_id)
    
    def get_agent_capabilities(self, agent_id: str) -> List[str]:
        """
        Get capabilities for a specific agent.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            List of capabilities
        """
        metadata = self.agent_metadata.get(agent_id, {})
        return metadata.get("capabilities", [])
    
    def get_agent_tools(self, agent_id: str) -> List[str]:
        """
        Get tools for a specific agent.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            List of tools
        """
        metadata = self.agent_metadata.get(agent_id, {})
        return metadata.get("tools", [])
    
    def get_agents_by_capability(self, capability: str) -> List[str]:
        """
        Get agents that have a specific capability.
        
        Args:
            capability: Capability to search for
            
        Returns:
            List of agent IDs
        """
        matching_agents = []
        
        for agent_id, metadata in self.agent_metadata.items():
            capabilities = metadata.get("capabilities", [])
            if capability in capabilities:
                matching_agents.append(agent_id)
        
        return matching_agents
    
    def get_agents_by_intent(self, intent: str) -> List[str]:
        """
        Get agents that support a specific intent.
        
        Args:
            intent: Intent to search for
            
        Returns:
            List of agent IDs
        """
        matching_agents = []
        
        for agent_id, metadata in self.agent_metadata.items():
            supported_intents = metadata.get("supported_intents", [])
            if intent in supported_intents:
                matching_agents.append(agent_id)
        
        return matching_agents
    
    def update_agent_config(
        self,
        agent_id: str,
        config_updates: Dict[str, Any]
    ) -> bool:
        """
        Update configuration for an agent.
        
        Args:
            agent_id: Agent identifier
            config_updates: Configuration updates
            
        Returns:
            True if successful
        """
        try:
            if agent_id not in self.agent_configs:
                self.logger.error(f"Agent not found: {agent_id}")
                return False
            
            self.agent_configs[agent_id].update(config_updates)
            
            # Update existing instance if it exists
            if agent_id in self.agent_instances:
                # Recreate instance with new config
                self.create_agent_node(agent_id, force_new=True)
            
            self.logger.info(f"Updated configuration for agent: {agent_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating agent config {agent_id}: {e}")
            return False
    
    def remove_agent(self, agent_id: str) -> bool:
        """
        Remove an agent from the factory.
        
        Args:
            agent_id: Agent identifier
            
        Returns:
            True if successful
        """
        try:
            # Remove from all registries
            self.agent_classes.pop(agent_id, None)
            self.agent_configs.pop(agent_id, None)
            self.agent_instances.pop(agent_id, None)
            self.agent_metadata.pop(agent_id, None)
            
            self.logger.info(f"Removed agent: {agent_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error removing agent {agent_id}: {e}")
            return False
    
    def discover_agents(self, discovery_paths: List[str]) -> int:
        """
        Discover and register agents from specified paths.
        
        Args:
            discovery_paths: List of module paths to search
            
        Returns:
            Number of agents discovered
        """
        discovered_count = 0
        
        for path in discovery_paths:
            try:
                module = importlib.import_module(path)
                
                # Look for agent node classes
                for name in dir(module):
                    obj = getattr(module, name)
                    
                    if (inspect.isclass(obj) and 
                        issubclass(obj, BaseAgentNode) and 
                        obj != BaseAgentNode):
                        
                        # Generate agent ID from class name
                        agent_id = name.lower().replace("agentnode", "").replace("node", "")
                        
                        if agent_id not in self.agent_configs:
                            self.register_agent_class(agent_id, obj)
                            discovered_count += 1
                
            except ImportError as e:
                self.logger.debug(f"Could not import {path}: {e}")
            except Exception as e:
                self.logger.error(f"Error discovering agents in {path}: {e}")
        
        self.logger.info(f"Discovered {discovered_count} new agents")
        
        return discovered_count
    
    def get_factory_stats(self) -> Dict[str, Any]:
        """
        Get factory statistics.
        
        Returns:
            Factory statistics
        """
        return {
            "registered_agents": len(self.agent_classes),
            "cached_instances": len(self.agent_instances),
            "total_capabilities": sum(
                len(metadata.get("capabilities", []))
                for metadata in self.agent_metadata.values()
            ),
            "total_tools": sum(
                len(metadata.get("tools", []))
                for metadata in self.agent_metadata.values()
            ),
            "agents": list(self.agent_classes.keys())
        }
    
    def clear_cache(self) -> None:
        """Clear the agent instance cache."""
        self.agent_instances.clear()
        self.logger.info("Cleared agent instance cache")


# Global factory instance
agent_factory = AgentNodeFactory()
