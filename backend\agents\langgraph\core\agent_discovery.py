"""
Agent Discovery Service for LangGraph Network Architecture.

This module provides agent discovery capabilities that enable agents to
find and connect with other agents based on capabilities, availability,
and collaboration patterns.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import json

from .agent_registry import NetworkAgentRegistry, NetworkAgent, network_registry
from ..states.network_state import NetworkDatageniusState

logger = logging.getLogger(__name__)


class DiscoveryStrategy(str, Enum):
    """Strategies for agent discovery."""
    CAPABILITY_BASED = "capability_based"
    PERFORMANCE_BASED = "performance_based"
    AVAILABILITY_BASED = "availability_based"
    COLLABORATION_HISTORY = "collaboration_history"
    HYBRID = "hybrid"


class MatchingCriteria(str, Enum):
    """Criteria for agent matching."""
    EXACT_MATCH = "exact_match"
    PARTIAL_MATCH = "partial_match"
    FUZZY_MATCH = "fuzzy_match"
    SEMANTIC_MATCH = "semantic_match"


@dataclass
class DiscoveryRequest:
    """Request for agent discovery."""
    requesting_agent: str
    required_capabilities: List[str]
    preferred_capabilities: Optional[List[str]] = None
    exclude_agents: Optional[List[str]] = None
    max_results: int = 5
    strategy: DiscoveryStrategy = DiscoveryStrategy.HYBRID
    criteria: MatchingCriteria = MatchingCriteria.PARTIAL_MATCH
    min_confidence: float = 0.7
    include_busy_agents: bool = False
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class DiscoveryResult:
    """Result of agent discovery."""
    agent: NetworkAgent
    match_score: float
    matching_capabilities: List[str]
    confidence_scores: Dict[str, float]
    availability_score: float
    collaboration_score: float
    recommendation_reason: str


class AgentDiscoveryService:
    """
    Service for discovering and matching agents in the network.
    
    This service provides:
    - Capability-based agent discovery
    - Performance-aware agent matching
    - Collaboration history analysis
    - Dynamic agent recommendations
    - Network topology optimization
    """

    def __init__(self, registry: NetworkAgentRegistry = None):
        """Initialize the discovery service."""
        self.registry = registry or network_registry
        self.discovery_cache: Dict[str, Tuple[List[DiscoveryResult], datetime]] = {}
        self.cache_ttl = timedelta(minutes=5)
        self.collaboration_weights = {
            "capability_match": 0.4,
            "performance_score": 0.3,
            "availability": 0.2,
            "collaboration_history": 0.1
        }
        
        logger.info("AgentDiscoveryService initialized")

    async def discover_agents(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """
        Discover agents based on the given request.
        
        Args:
            request: Discovery request with criteria
            
        Returns:
            List of discovery results sorted by match score
        """
        try:
            # Check cache first
            cache_key = self._create_cache_key(request)
            if cache_key in self.discovery_cache:
                cached_results, cached_time = self.discovery_cache[cache_key]
                if datetime.now() - cached_time < self.cache_ttl:
                    logger.debug(f"Returning cached discovery results for {request.requesting_agent}")
                    return cached_results[:request.max_results]

            # Perform discovery based on strategy
            if request.strategy == DiscoveryStrategy.CAPABILITY_BASED:
                results = await self._capability_based_discovery(request)
            elif request.strategy == DiscoveryStrategy.PERFORMANCE_BASED:
                results = await self._performance_based_discovery(request)
            elif request.strategy == DiscoveryStrategy.AVAILABILITY_BASED:
                results = await self._availability_based_discovery(request)
            elif request.strategy == DiscoveryStrategy.COLLABORATION_HISTORY:
                results = await self._collaboration_history_discovery(request)
            else:  # HYBRID
                results = await self._hybrid_discovery(request)

            # Sort by match score
            results.sort(key=lambda r: r.match_score, reverse=True)
            
            # Limit results
            limited_results = results[:request.max_results]
            
            # Cache results
            self.discovery_cache[cache_key] = (limited_results, datetime.now())
            
            logger.info(f"Discovered {len(limited_results)} agents for {request.requesting_agent}")
            return limited_results

        except Exception as e:
            logger.error(f"Error in agent discovery: {e}")
            return []

    async def _capability_based_discovery(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """Discover agents based on capabilities."""
        results = []
        exclude_agents = set(request.exclude_agents or [])
        exclude_agents.add(request.requesting_agent)  # Don't include requesting agent

        for agent in self.registry.get_all_agents():
            if agent.agent_id in exclude_agents:
                continue
                
            if not request.include_busy_agents and not agent.is_available():
                continue

            # Calculate capability match
            match_score, matching_caps, confidence_scores = self._calculate_capability_match(
                agent, request.required_capabilities, request.preferred_capabilities or []
            )

            if match_score < request.min_confidence:
                continue

            # Calculate other scores
            availability_score = self._calculate_availability_score(agent)
            collaboration_score = self._calculate_collaboration_score(agent, request.requesting_agent)

            # Create result
            result = DiscoveryResult(
                agent=agent,
                match_score=match_score,
                matching_capabilities=matching_caps,
                confidence_scores=confidence_scores,
                availability_score=availability_score,
                collaboration_score=collaboration_score,
                recommendation_reason=f"Strong capability match ({len(matching_caps)} capabilities)"
            )
            
            results.append(result)

        return results

    async def _performance_based_discovery(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """Discover agents based on performance metrics."""
        results = []
        exclude_agents = set(request.exclude_agents or [])
        exclude_agents.add(request.requesting_agent)

        for agent in self.registry.get_all_agents():
            if agent.agent_id in exclude_agents:
                continue
                
            if not request.include_busy_agents and not agent.is_available():
                continue

            # Check if agent has required capabilities
            has_required_caps = all(
                agent.can_handle_capability(cap, request.min_confidence)
                for cap in request.required_capabilities
            )
            
            if not has_required_caps:
                continue

            # Calculate performance-weighted score
            performance_score = self._calculate_performance_score(agent)
            capability_match, matching_caps, confidence_scores = self._calculate_capability_match(
                agent, request.required_capabilities, request.preferred_capabilities or []
            )
            
            # Weight performance more heavily
            match_score = performance_score * 0.7 + capability_match * 0.3
            
            availability_score = self._calculate_availability_score(agent)
            collaboration_score = self._calculate_collaboration_score(agent, request.requesting_agent)

            result = DiscoveryResult(
                agent=agent,
                match_score=match_score,
                matching_capabilities=matching_caps,
                confidence_scores=confidence_scores,
                availability_score=availability_score,
                collaboration_score=collaboration_score,
                recommendation_reason=f"High performance agent (score: {performance_score:.2f})"
            )
            
            results.append(result)

        return results

    async def _availability_based_discovery(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """Discover agents based on availability."""
        results = []
        exclude_agents = set(request.exclude_agents or [])
        exclude_agents.add(request.requesting_agent)

        for agent in self.registry.get_all_agents():
            if agent.agent_id in exclude_agents:
                continue

            # Check capability requirements
            has_required_caps = all(
                agent.can_handle_capability(cap, request.min_confidence)
                for cap in request.required_capabilities
            )
            
            if not has_required_caps:
                continue

            availability_score = self._calculate_availability_score(agent)
            
            # Skip unavailable agents unless explicitly included
            if not request.include_busy_agents and availability_score < 0.5:
                continue

            capability_match, matching_caps, confidence_scores = self._calculate_capability_match(
                agent, request.required_capabilities, request.preferred_capabilities or []
            )
            
            # Weight availability more heavily
            match_score = availability_score * 0.6 + capability_match * 0.4
            
            collaboration_score = self._calculate_collaboration_score(agent, request.requesting_agent)

            result = DiscoveryResult(
                agent=agent,
                match_score=match_score,
                matching_capabilities=matching_caps,
                confidence_scores=confidence_scores,
                availability_score=availability_score,
                collaboration_score=collaboration_score,
                recommendation_reason=f"Highly available agent (availability: {availability_score:.2f})"
            )
            
            results.append(result)

        return results

    async def _collaboration_history_discovery(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """Discover agents based on collaboration history."""
        results = []
        exclude_agents = set(request.exclude_agents or [])
        exclude_agents.add(request.requesting_agent)

        for agent in self.registry.get_all_agents():
            if agent.agent_id in exclude_agents:
                continue
                
            if not request.include_busy_agents and not agent.is_available():
                continue

            # Check capability requirements
            has_required_caps = all(
                agent.can_handle_capability(cap, request.min_confidence)
                for cap in request.required_capabilities
            )
            
            if not has_required_caps:
                continue

            collaboration_score = self._calculate_collaboration_score(agent, request.requesting_agent)
            capability_match, matching_caps, confidence_scores = self._calculate_capability_match(
                agent, request.required_capabilities, request.preferred_capabilities or []
            )
            
            # Weight collaboration history more heavily
            match_score = collaboration_score * 0.6 + capability_match * 0.4
            
            availability_score = self._calculate_availability_score(agent)

            result = DiscoveryResult(
                agent=agent,
                match_score=match_score,
                matching_capabilities=matching_caps,
                confidence_scores=confidence_scores,
                availability_score=availability_score,
                collaboration_score=collaboration_score,
                recommendation_reason=f"Strong collaboration history (score: {collaboration_score:.2f})"
            )
            
            results.append(result)

        return results

    async def _hybrid_discovery(self, request: DiscoveryRequest) -> List[DiscoveryResult]:
        """Discover agents using hybrid approach combining all factors."""
        results = []
        exclude_agents = set(request.exclude_agents or [])
        exclude_agents.add(request.requesting_agent)

        for agent in self.registry.get_all_agents():
            if agent.agent_id in exclude_agents:
                continue
                
            if not request.include_busy_agents and not agent.is_available():
                continue

            # Check capability requirements
            has_required_caps = all(
                agent.can_handle_capability(cap, request.min_confidence)
                for cap in request.required_capabilities
            )
            
            if not has_required_caps:
                continue

            # Calculate all scores
            capability_match, matching_caps, confidence_scores = self._calculate_capability_match(
                agent, request.required_capabilities, request.preferred_capabilities or []
            )
            performance_score = self._calculate_performance_score(agent)
            availability_score = self._calculate_availability_score(agent)
            collaboration_score = self._calculate_collaboration_score(agent, request.requesting_agent)

            # Calculate weighted hybrid score
            match_score = (
                capability_match * self.collaboration_weights["capability_match"] +
                performance_score * self.collaboration_weights["performance_score"] +
                availability_score * self.collaboration_weights["availability"] +
                collaboration_score * self.collaboration_weights["collaboration_history"]
            )

            result = DiscoveryResult(
                agent=agent,
                match_score=match_score,
                matching_capabilities=matching_caps,
                confidence_scores=confidence_scores,
                availability_score=availability_score,
                collaboration_score=collaboration_score,
                recommendation_reason=f"Balanced match across all criteria (score: {match_score:.2f})"
            )
            
            results.append(result)

        return results

    def _calculate_capability_match(
        self,
        agent: NetworkAgent,
        required_capabilities: List[str],
        preferred_capabilities: List[str]
    ) -> Tuple[float, List[str], Dict[str, float]]:
        """Calculate capability match score."""
        matching_capabilities = []
        confidence_scores = {}
        total_score = 0.0
        max_possible_score = 0.0

        # Required capabilities (weighted more heavily)
        for cap in required_capabilities:
            max_possible_score += 2.0  # Weight required capabilities as 2x
            confidence = agent.get_capability_confidence(cap)
            if confidence > 0:
                matching_capabilities.append(cap)
                confidence_scores[cap] = confidence
                total_score += confidence * 2.0

        # Preferred capabilities
        for cap in preferred_capabilities:
            max_possible_score += 1.0
            confidence = agent.get_capability_confidence(cap)
            if confidence > 0:
                if cap not in matching_capabilities:
                    matching_capabilities.append(cap)
                confidence_scores[cap] = confidence
                total_score += confidence

        # Normalize score
        match_score = total_score / max_possible_score if max_possible_score > 0 else 0.0
        
        return match_score, matching_capabilities, confidence_scores

    def _calculate_performance_score(self, agent: NetworkAgent) -> float:
        """Calculate performance score based on metrics."""
        if not agent.performance_metrics:
            return 0.5  # Default score for agents without metrics
        
        # Average all performance metrics
        total_score = sum(agent.performance_metrics.values())
        return total_score / len(agent.performance_metrics)

    def _calculate_availability_score(self, agent: NetworkAgent) -> float:
        """Calculate availability score."""
        if not agent.is_available():
            return 0.0
        
        # Calculate based on current load
        load_ratio = agent.current_load / agent.max_concurrent_tasks
        availability_score = 1.0 - load_ratio
        
        # Bonus for idle agents
        if agent.current_load == 0:
            availability_score += 0.1
        
        return min(1.0, availability_score)

    def _calculate_collaboration_score(self, agent: NetworkAgent, requesting_agent: str) -> float:
        """Calculate collaboration score based on history."""
        # Get collaboration patterns from registry
        patterns = self.registry.collaboration_patterns.get(requesting_agent, {})
        collaboration_count = patterns.get(agent.agent_id, 0)
        
        # Normalize based on total collaborations
        total_collaborations = sum(patterns.values()) if patterns else 1
        collaboration_score = collaboration_count / total_collaborations
        
        return min(1.0, collaboration_score)

    def _create_cache_key(self, request: DiscoveryRequest) -> str:
        """Create cache key for discovery request."""
        key_data = {
            "requesting_agent": request.requesting_agent,
            "required_capabilities": sorted(request.required_capabilities),
            "preferred_capabilities": sorted(request.preferred_capabilities or []),
            "exclude_agents": sorted(request.exclude_agents or []),
            "strategy": request.strategy.value,
            "criteria": request.criteria.value,
            "min_confidence": request.min_confidence,
            "include_busy_agents": request.include_busy_agents
        }
        return json.dumps(key_data, sort_keys=True)

    async def find_specialist_for_capability(
        self,
        capability: str,
        requesting_agent: str,
        min_confidence: float = 0.8
    ) -> Optional[NetworkAgent]:
        """Find the best specialist for a specific capability."""
        request = DiscoveryRequest(
            requesting_agent=requesting_agent,
            required_capabilities=[capability],
            max_results=1,
            strategy=DiscoveryStrategy.PERFORMANCE_BASED,
            min_confidence=min_confidence
        )
        
        results = await self.discover_agents(request)
        return results[0].agent if results else None

    async def find_collaboration_partners(
        self,
        requesting_agent: str,
        task_capabilities: List[str],
        team_size: int = 3
    ) -> List[NetworkAgent]:
        """Find agents for collaborative work."""
        request = DiscoveryRequest(
            requesting_agent=requesting_agent,
            required_capabilities=task_capabilities,
            max_results=team_size,
            strategy=DiscoveryStrategy.HYBRID,
            include_busy_agents=False
        )
        
        results = await self.discover_agents(request)
        return [result.agent for result in results]

    def clear_cache(self):
        """Clear the discovery cache."""
        self.discovery_cache.clear()
        logger.info("Discovery cache cleared")


# Global discovery service instance
discovery_service = AgentDiscoveryService()
