"""
Test suite for the configurable persona migration.

This test validates that the migration from hardcoded strategy classes to 
ConfigurablePersonaStrategy works correctly and eliminates infinite loops.
"""

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any

from ..strategies.extensible_strategy_system import ConfigurablePersonaStrategy
from ..states.unified_state import create_unified_state, UnifiedDatageniusState
from ..nodes.unified_persona_node import UnifiedPersonaNode


class TestConfigurablePersonaMigration:
    """Test suite for configurable persona migration."""

    @pytest.fixture
    def sample_concierge_config(self) -> Dict[str, Any]:
        """Sample concierge configuration for testing."""
        return {
            "id": "concierge",
            "persona_id": "concierge",
            "name": "Datagenius Concierge",
            "agent_type": "concierge",
            "capabilities": ["persona_recommendation", "conversation_management"],
            "llm_config": {
                "provider": "groq",
                "model": "mixtral-8x7b-32768",
                "temperature": 0.7,
                "max_tokens": 4000
            },
            "prompt_templates": {
                "system_prompt": "You are a helpful Datagenius Concierge.",
                "default": "I'm here to help! How can I assist you today?"
            },
            "response_templates": {
                "default": "Hello! I'm your Datagenius Concierge. How can I help you today?"
            },
            "processing_rules": {
                "processing_pipeline": [
                    {
                        "name": "context_extraction",
                        "type": "context_extraction",
                        "extraction_rules": {
                            "user_message": {
                                "type": "state_lookup",
                                "key": "user_message",
                                "default": ""
                            }
                        }
                    },
                    {
                        "name": "response_generation",
                        "type": "response_generation"
                    }
                ]
            }
        }

    @pytest.fixture
    def sample_state(self) -> UnifiedDatageniusState:
        """Sample state for testing."""
        return create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            initial_message={
                "id": "msg_1",
                "content": "Hello, I need help with data analysis",
                "role": "user",
                "timestamp": "2024-01-01T00:00:00Z"
            }
        )

    def test_configurable_strategy_creation(self, sample_concierge_config):
        """Test that ConfigurablePersonaStrategy can be created with configuration."""
        strategy = ConfigurablePersonaStrategy(sample_concierge_config)
        
        assert strategy.config == sample_concierge_config
        assert strategy.capabilities == ["persona_recommendation", "conversation_management"]
        assert "processing_pipeline" in strategy.processing_rules

    @patch('backend.agents.langgraph.strategies.extensible_strategy_system.SharedLLMProcessor')
    def test_configurable_strategy_llm_initialization(self, mock_llm_processor, sample_concierge_config):
        """Test that LLM processor is initialized correctly."""
        strategy = ConfigurablePersonaStrategy(sample_concierge_config)
        
        # Verify LLM processor was attempted to be initialized
        mock_llm_processor.assert_called_once_with(sample_concierge_config["llm_config"])

    @pytest.mark.asyncio
    async def test_response_generation_no_infinite_loop(self, sample_concierge_config, sample_state):
        """Test that response generation doesn't cause infinite loops."""
        strategy = ConfigurablePersonaStrategy(sample_concierge_config)
        
        # Mock the LLM processor to return a response
        with patch.object(strategy, '_call_llm', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = "I'm here to help you with data analysis!"
            
            context = {"user_id": "test_user", "conversation_id": "test_conversation"}
            
            # Process message - this should not loop infinitely
            result_state = await strategy.process_message(sample_state, context)
            
            # Verify response was generated
            assert len(result_state["messages"]) > len(sample_state["messages"])
            
            # Verify the response message was added
            response_message = result_state["messages"][-1]
            assert response_message["role"] == "assistant"
            assert response_message["content"] == "I'm here to help you with data analysis!"
            assert result_state["workflow_context"]["response_generated"] is True

    @pytest.mark.asyncio
    async def test_fallback_response_on_llm_failure(self, sample_concierge_config, sample_state):
        """Test that fallback response is generated when LLM fails."""
        strategy = ConfigurablePersonaStrategy(sample_concierge_config)
        
        # Mock LLM processor to fail
        with patch.object(strategy, '_call_llm', new_callable=AsyncMock) as mock_llm:
            mock_llm.side_effect = Exception("LLM failed")
            
            context = {"user_id": "test_user", "conversation_id": "test_conversation"}
            
            # Process message - should use template fallback
            result_state = await strategy.process_message(sample_state, context)
            
            # Verify fallback response was generated
            assert len(result_state["messages"]) > len(sample_state["messages"])
            response_message = result_state["messages"][-1]
            assert response_message["role"] == "assistant"
            assert "I'm here to help!" in response_message["content"]

    def test_unified_persona_node_uses_configurable_strategy(self, sample_concierge_config):
        """Test that UnifiedPersonaNode uses ConfigurablePersonaStrategy."""
        with patch('backend.agents.langgraph.nodes.unified_persona_node.MCPToolManager'), \
             patch('backend.agents.langgraph.nodes.unified_persona_node.BusinessContextManager'), \
             patch('backend.agents.langgraph.nodes.unified_persona_node.CrossAgentIntelligenceManager'):
            
            node = UnifiedPersonaNode(sample_concierge_config)
            
            # Verify the strategy is ConfigurablePersonaStrategy
            assert isinstance(node.persona_strategy, ConfigurablePersonaStrategy)
            assert node.persona_strategy.config == sample_concierge_config

    @pytest.mark.asyncio
    async def test_processing_pipeline_execution(self, sample_concierge_config, sample_state):
        """Test that processing pipeline steps are executed correctly."""
        strategy = ConfigurablePersonaStrategy(sample_concierge_config)
        
        context = {"user_id": "test_user", "conversation_id": "test_conversation"}
        
        with patch.object(strategy, '_call_llm', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = "Test response"
            
            result_state = await strategy.process_message(sample_state, context)
            
            # Verify context extraction was performed
            assert "user_message" in result_state["workflow_context"]
            
            # Verify response generation was triggered
            assert result_state["workflow_context"]["response_generated"] is True

    def test_template_rendering(self, sample_concierge_config):
        """Test that template rendering works correctly."""
        strategy = ConfigurablePersonaStrategy(sample_concierge_config)
        
        template = "Hello {user_name}, welcome to {platform}!"
        context = {"user_name": "John", "platform": "Datagenius"}
        
        rendered = strategy._render_template(template, context)
        assert rendered == "Hello John, welcome to Datagenius!"

    def test_system_prompt_generation(self, sample_concierge_config):
        """Test that system prompt is generated from configuration."""
        strategy = ConfigurablePersonaStrategy(sample_concierge_config)
        
        context = {"business_context": "Test business", "capabilities": "test, help"}
        prompt = strategy.get_system_prompt(context)
        
        assert "Datagenius Concierge" in prompt

    @pytest.mark.asyncio
    async def test_error_handling_in_processing_steps(self, sample_concierge_config, sample_state):
        """Test that errors in processing steps are handled gracefully."""
        # Create config with invalid processing step
        invalid_config = sample_concierge_config.copy()
        invalid_config["processing_rules"]["processing_pipeline"].append({
            "name": "invalid_step",
            "type": "non_existent_type"
        })
        
        strategy = ConfigurablePersonaStrategy(invalid_config)
        context = {"user_id": "test_user"}
        
        with patch.object(strategy, '_call_llm', new_callable=AsyncMock) as mock_llm:
            mock_llm.return_value = "Test response"
            
            # Should not raise exception despite invalid step
            result_state = await strategy.process_message(sample_state, context)
            
            # Should still generate response
            assert len(result_state["messages"]) > len(sample_state["messages"])

    def test_no_hardcoded_strategy_classes_imported(self):
        """Test that hardcoded strategy classes are not imported."""
        from ..nodes.unified_persona_node import UnifiedPersonaNode
        
        # These classes should not exist anymore
        with pytest.raises(AttributeError):
            from ..nodes.unified_persona_node import ConciergePersonaStrategy
        
        with pytest.raises(AttributeError):
            from ..nodes.unified_persona_node import MarketingPersonaStrategy
        
        with pytest.raises(AttributeError):
            from ..nodes.unified_persona_node import AnalysisPersonaStrategy
        
        with pytest.raises(AttributeError):
            from ..nodes.unified_persona_node import ClassificationPersonaStrategy


if __name__ == "__main__":
    pytest.main([__file__])
