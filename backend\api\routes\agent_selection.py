"""
Agent Selection API Routes

This module provides API endpoints for user-controlled agent selection
and direct agent communication in the new user-controlled collaboration system.
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from pydantic import BaseModel, Field

from ...agents.langgraph.core.agent_registry import network_registry
from ...agents.langgraph.agents.user_selected_concierge_agent import UserSelectedConciergeAgent
from ...agents.langgraph.agents.user_selected_analysis_agent import UserSelectedAnalysisAgent
from ...agents.langgraph.agents.user_selected_marketing_agent import UserSelectedMarketingAgent
from ...agents.langgraph.agents.user_selected_visualization_agent import UserSelectedVisualizationAgent
from ...agents.langgraph.permissions.user_permission_manager import UserPermissionManager, PermissionResponse
from ...agents.langgraph.flows.permission_based_collaboration_flow import (
    permission_based_collaboration_flow,
    CollaborationFlowRequest
)
from ...core.auth import get_current_user
from ...core.database import get_db

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/agent-selection", tags=["agent-selection"])


# Request/Response Models
class AgentSelectionRequest(BaseModel):
    """Request to select an agent for conversation."""
    agent_id: str = Field(..., description="ID of the agent to select")
    conversation_id: Optional[str] = Field(None, description="Existing conversation ID")
    business_profile_id: Optional[str] = Field(None, description="Business profile context")


class AgentMessageRequest(BaseModel):
    """Request to send a message to a selected agent."""
    agent_id: str = Field(..., description="ID of the selected agent")
    message: str = Field(..., description="Message to send to the agent")
    conversation_id: str = Field(..., description="Conversation ID")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")


class PermissionRequestResponse(BaseModel):
    """Response to a permission request."""
    request_id: str = Field(..., description="ID of the permission request")
    approved: bool = Field(..., description="Whether permission is granted")
    reason: Optional[str] = Field(None, description="Reason for the decision")


class AgentInfo(BaseModel):
    """Information about an available agent."""
    agent_id: str
    name: str
    description: str
    agent_type: str
    capabilities: List[str]
    priority: int
    status: str
    is_available: bool


class AgentSelectionResponse(BaseModel):
    """Response for agent selection."""
    success: bool
    agent_id: str
    conversation_id: str
    agent_info: AgentInfo
    message: Optional[str] = None


class AgentMessageResponse(BaseModel):
    """Response from an agent message."""
    success: bool
    message: str
    metadata: Dict[str, Any]
    collaboration_requests: Optional[List[Dict[str, Any]]] = None


# Agent Selection Endpoints
@router.get("/available-agents", response_model=List[AgentInfo])
async def get_available_agents(current_user: Dict = Depends(get_current_user)):
    """
    Get list of agents available for user selection.
    
    Returns:
        List of available agents that support user selection
    """
    try:
        logger.info(f"Getting available agents for user {current_user.get('user_id')}")
        
        # Get user-selectable agents from registry
        selectable_agents = network_registry.get_user_selectable_agents()
        
        agent_list = []
        for agent in selectable_agents:
            agent_info = AgentInfo(
                agent_id=agent.agent_id,
                name=agent.user_friendly_name,
                description=agent.user_description,
                agent_type=agent.agent_type,
                capabilities=[cap.name for cap in agent.capabilities],
                priority=agent.user_selection_priority,
                status=agent.status.value,
                is_available=agent.is_available()
            )
            agent_list.append(agent_info)
        
        logger.info(f"Found {len(agent_list)} available agents")
        return agent_list
        
    except Exception as e:
        logger.error(f"Error getting available agents: {e}")
        raise HTTPException(status_code=500, detail="Failed to get available agents")


@router.post("/select-agent", response_model=AgentSelectionResponse)
async def select_agent(
    request: AgentSelectionRequest,
    current_user: Dict = Depends(get_current_user)
):
    """
    Select an agent for direct conversation.
    
    Args:
        request: Agent selection request
        current_user: Current authenticated user
        
    Returns:
        Agent selection response with conversation details
    """
    try:
        user_id = current_user.get("user_id")
        logger.info(f"User {user_id} selecting agent {request.agent_id}")
        
        # Validate agent exists and supports user selection
        agent = network_registry.get_agent_for_user_selection(request.agent_id)
        if not agent:
            raise HTTPException(
                status_code=404, 
                detail=f"Agent {request.agent_id} not found or not available for selection"
            )
        
        # Generate conversation ID if not provided
        conversation_id = request.conversation_id or str(uuid.uuid4())
        
        # Create agent info response
        agent_info = AgentInfo(
            agent_id=agent.agent_id,
            name=agent.user_friendly_name,
            description=agent.user_description,
            agent_type=agent.agent_type,
            capabilities=[cap.name for cap in agent.capabilities],
            priority=agent.user_selection_priority,
            status=agent.status.value,
            is_available=agent.is_available()
        )
        
        response = AgentSelectionResponse(
            success=True,
            agent_id=request.agent_id,
            conversation_id=conversation_id,
            agent_info=agent_info,
            message=f"Connected to {agent.user_friendly_name}. How can I help you today?"
        )
        
        logger.info(f"Successfully selected agent {request.agent_id} for user {user_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error selecting agent: {e}")
        raise HTTPException(status_code=500, detail="Failed to select agent")


@router.post("/agent-message", response_model=AgentMessageResponse)
async def send_agent_message(
    request: AgentMessageRequest,
    background_tasks: BackgroundTasks,
    current_user: Dict = Depends(get_current_user)
):
    """
    Send a message to a selected agent.
    
    Args:
        request: Agent message request
        background_tasks: Background tasks for async processing
        current_user: Current authenticated user
        
    Returns:
        Agent response
    """
    try:
        user_id = current_user.get("user_id")
        logger.info(f"User {user_id} sending message to agent {request.agent_id}")
        
        # Get the appropriate user-selected agent instance
        agent_instance = _get_user_selected_agent_instance(request.agent_id)
        if not agent_instance:
            raise HTTPException(
                status_code=404,
                detail=f"Agent {request.agent_id} not available"
            )
        
        # Prepare context
        context = request.context or {}
        context.update({
            "user_id": user_id,
            "conversation_id": request.conversation_id,
            "business_profile_id": current_user.get("business_profile_id"),
            "timestamp": datetime.now().isoformat()
        })
        
        # Process message with the agent
        agent_response = await agent_instance.process_user_message(
            message=request.message,
            user_id=user_id,
            conversation_id=request.conversation_id,
            context=context
        )
        
        # Extract collaboration requests if any
        collaboration_requests = agent_response.get("metadata", {}).get("collaboration_requests", [])
        
        response = AgentMessageResponse(
            success=True,
            message=agent_response.get("message", ""),
            metadata=agent_response.get("metadata", {}),
            collaboration_requests=collaboration_requests if collaboration_requests else None
        )
        
        logger.info(f"Successfully processed message for agent {request.agent_id}")
        return response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error processing agent message: {e}")
        raise HTTPException(status_code=500, detail="Failed to process message")


@router.post("/permission-response")
async def handle_permission_response(
    response: PermissionRequestResponse,
    current_user: Dict = Depends(get_current_user)
):
    """
    Handle user response to a permission request.
    
    Args:
        response: Permission response from user
        current_user: Current authenticated user
        
    Returns:
        Success confirmation
    """
    try:
        user_id = current_user.get("user_id")
        logger.info(f"User {user_id} responding to permission request {response.request_id}")
        
        # Create permission response object
        permission_response = PermissionResponse(
            request_id=response.request_id,
            approved=response.approved,
            user_id=user_id,
            reason=response.reason
        )
        
        # Handle the permission response
        permission_manager = UserPermissionManager()
        success = await permission_manager.handle_permission_response(permission_response)
        
        if success:
            logger.info(f"Permission response processed successfully: {response.request_id}")
            return {"success": True, "message": "Permission response processed"}
        else:
            raise HTTPException(status_code=400, detail="Failed to process permission response")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error handling permission response: {e}")
        raise HTTPException(status_code=500, detail="Failed to handle permission response")


@router.get("/agent-info/{agent_id}", response_model=AgentInfo)
async def get_agent_info(
    agent_id: str,
    current_user: Dict = Depends(get_current_user)
):
    """
    Get detailed information about a specific agent.
    
    Args:
        agent_id: ID of the agent
        current_user: Current authenticated user
        
    Returns:
        Detailed agent information
    """
    try:
        logger.info(f"Getting info for agent {agent_id}")
        
        agent = network_registry.get_agent_for_user_selection(agent_id)
        if not agent:
            raise HTTPException(status_code=404, detail="Agent not found")
        
        agent_info = AgentInfo(
            agent_id=agent.agent_id,
            name=agent.user_friendly_name,
            description=agent.user_description,
            agent_type=agent.agent_type,
            capabilities=[cap.name for cap in agent.capabilities],
            priority=agent.user_selection_priority,
            status=agent.status.value,
            is_available=agent.is_available()
        )
        
        return agent_info
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting agent info: {e}")
        raise HTTPException(status_code=500, detail="Failed to get agent info")


def _get_user_selected_agent_instance(agent_id: str):
    """Get the appropriate user-selected agent instance."""
    try:
        if agent_id == "user_selected_concierge":
            return UserSelectedConciergeAgent()
        elif agent_id == "user_selected_analysis":
            return UserSelectedAnalysisAgent()
        elif agent_id == "user_selected_marketing":
            return UserSelectedMarketingAgent()
        elif agent_id == "user_selected_visualization":
            return UserSelectedVisualizationAgent()
        else:
            logger.warning(f"Unknown agent ID: {agent_id}")
            return None
    except Exception as e:
        logger.error(f"Error creating agent instance for {agent_id}: {e}")
        return None
