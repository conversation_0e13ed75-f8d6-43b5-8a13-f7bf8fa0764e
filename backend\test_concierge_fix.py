#!/usr/bin/env python3
"""
Test script to verify the concierge agent fix.

This script tests that the concierge agent now uses the conversation tool
instead of the fallback agent.
"""

import sys
import os
import asyncio
import logging

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_concierge_agent():
    """Test the concierge agent with conversation tool."""
    try:
        logger.info("🧪 Testing concierge agent with conversation tool...")
        
        # Test 1: Create concierge agent
        logger.info("🤖 Test 1: Creating concierge agent...")
        from agents.langgraph.agents.concierge_agent import UserSelectedConciergeAgent
        
        agent = UserSelectedConciergeAgent({'agent_id': 'concierge'})
        logger.info("✅ Concierge agent created successfully")
        
        # Test 2: Test basic message processing
        logger.info("💬 Test 2: Testing basic message processing...")
        response = await agent.process_message(
            message="Hello, what tools are available?",
            user_id="test_user",
            conversation_id="test_conv",
            context={
                "conversation_history": [],
                "business_profile": {},
                "is_initial_greeting": False
            }
        )
        
        logger.info(f"✅ Response received: {response.get('message', '')[:100]}...")
        logger.info(f"✅ Metadata: {response.get('metadata', {})}")
        
        # Check if it's using the conversation tool (not fallback)
        metadata = response.get('metadata', {})
        if metadata.get('fallback_mode'):
            logger.warning("⚠️ Agent is still using fallback mode")
        else:
            logger.info("✅ Agent is using proper conversation tool")
        
        # Test 3: Test greeting generation
        logger.info("👋 Test 3: Testing greeting generation...")
        greeting_response = await agent.process_message(
            message="generate_greeting",
            user_id="test_user",
            conversation_id="test_conv",
            context={
                "is_initial_greeting": True,
                "user_name": "Test User"
            }
        )
        
        logger.info(f"✅ Greeting response: {greeting_response.get('message', '')[:100]}...")
        
        # Test 4: Test workflow completion flags
        logger.info("🏁 Test 4: Checking workflow completion flags...")
        metadata = response.get('metadata', {})
        if metadata.get('workflow_complete') and metadata.get('next_action') == 'END':
            logger.info("✅ Workflow completion flags are set correctly")
        else:
            logger.warning("⚠️ Workflow completion flags are missing or incorrect")
        
        logger.info("🎉 All tests completed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_agent_factory():
    """Test the agent factory with the fixed concierge."""
    try:
        logger.info("🏭 Testing agent factory...")
        
        from agents.langgraph.core.agent_factory import agent_factory
        
        # Load registry
        agent_factory.load_agent_registry()
        logger.info("✅ Agent registry loaded")
        
        # Create concierge node
        concierge_node = agent_factory.create_agent_node('concierge')
        if concierge_node and concierge_node.agent_instance:
            logger.info("✅ Concierge node created with agent instance")
            
            # Check if it's the right type
            agent_type = type(concierge_node.agent_instance).__name__
            logger.info(f"✅ Agent instance type: {agent_type}")
            
            if "Fallback" in agent_type:
                logger.warning("⚠️ Still using fallback agent")
            else:
                logger.info("✅ Using proper concierge agent")
                
        else:
            logger.error("❌ Failed to create concierge node or agent instance")
            
        return True
        
    except Exception as e:
        logger.error(f"❌ Agent factory test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    async def run_all_tests():
        logger.info("🚀 Starting concierge agent tests...")
        
        test1_success = await test_concierge_agent()
        test2_success = await test_agent_factory()
        
        if test1_success and test2_success:
            print("\n✅ All tests passed! Concierge agent is working correctly.")
            return True
        else:
            print("\n❌ Some tests failed. Check the logs above.")
            return False
    
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)
