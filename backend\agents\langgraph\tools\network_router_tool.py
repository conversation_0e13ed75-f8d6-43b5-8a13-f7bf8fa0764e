"""
Network Router Tool

This module provides a tool that agents can use to leverage network routing
capabilities for collaboration. The router is no longer used for automatic
routing but serves as a tool for user-selected agents to coordinate with
other agents when needed.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

from ..core.agent_registry import network_registry, NetworkAgent
from ..core.agent_discovery import discovery_service, DiscoveryRequest, DiscoveryStrategy
from ..communication.messaging_system import messaging_system, NetworkCommunicationType, NetworkMessagePriority

logger = logging.getLogger(__name__)


class CollaborationPattern(Enum):
    """Types of collaboration patterns."""
    CONSULTATION = "consultation"
    DELEGATION = "delegation"
    PARALLEL_PROCESSING = "parallel_processing"
    SEQUENTIAL_PROCESSING = "sequential_processing"
    CONSENSUS_BUILDING = "consensus_building"


@dataclass
class CollaborationRequest:
    """Request for network collaboration."""
    requesting_agent: str
    collaboration_pattern: CollaborationPattern
    task_description: str
    required_capabilities: List[str]
    priority: NetworkMessagePriority = NetworkMessagePriority.NORMAL
    timeout_seconds: int = 60
    context: Optional[Dict[str, Any]] = None
    user_approved: bool = False


@dataclass
class CollaborationResult:
    """Result from network collaboration."""
    success: bool
    results: Dict[str, Any]
    participating_agents: List[str]
    execution_time_seconds: float
    pattern_used: CollaborationPattern
    metadata: Optional[Dict[str, Any]] = None


class NetworkRouterTool:
    """
    Network router tool for agent collaboration.
    
    This tool enables user-selected agents to:
    - Discover suitable specialists for collaboration
    - Coordinate multi-agent workflows
    - Manage agent-to-agent communication
    - Execute various collaboration patterns
    """

    def __init__(self, requesting_agent_id: str):
        """
        Initialize the network router tool.
        
        Args:
            requesting_agent_id: ID of the agent using this tool
        """
        self.requesting_agent_id = requesting_agent_id
        self.active_collaborations: Dict[str, CollaborationRequest] = {}
        self.collaboration_history: List[CollaborationResult] = []
        
        # Configuration
        self.max_concurrent_collaborations = 5
        self.default_timeout = 60
        self.max_agents_per_collaboration = 10
        
        logger.info(f"NetworkRouterTool initialized for agent {requesting_agent_id}")

    async def discover_specialists(self, required_capabilities: List[str],
                                 exclude_agents: Optional[List[str]] = None,
                                 max_results: int = 5) -> List[NetworkAgent]:
        """
        Discover specialists with required capabilities.
        
        Args:
            required_capabilities: List of required capabilities
            exclude_agents: List of agent IDs to exclude
            max_results: Maximum number of results to return
            
        Returns:
            List of suitable specialist agents
        """
        try:
            logger.debug(f"Discovering specialists for capabilities: {required_capabilities}")
            
            # Create discovery request
            discovery_request = DiscoveryRequest(
                requesting_agent=self.requesting_agent_id,
                required_capabilities=required_capabilities,
                exclude_agents=(exclude_agents or []) + [self.requesting_agent_id],
                max_results=max_results,
                strategy=DiscoveryStrategy.PERFORMANCE_BASED
            )
            
            # Discover agents
            discovery_results = await discovery_service.discover_agents(discovery_request)
            
            # Extract agents from results
            specialists = [result.agent for result in discovery_results]
            
            logger.info(f"Found {len(specialists)} specialists for capabilities: {required_capabilities}")
            return specialists
            
        except Exception as e:
            logger.error(f"Error discovering specialists: {e}")
            return []

    async def coordinate_consultation(self, specialist_id: str, query: str,
                                    context: Optional[Dict[str, Any]] = None,
                                    timeout_seconds: Optional[int] = None) -> Dict[str, Any]:
        """
        Coordinate a consultation with a specialist agent.
        
        Args:
            specialist_id: ID of the specialist agent
            query: Query or task for the specialist
            context: Additional context for the consultation
            timeout_seconds: Custom timeout for the consultation
            
        Returns:
            Consultation result
        """
        try:
            logger.info(f"Coordinating consultation with specialist {specialist_id}")
            
            # Validate specialist exists and is available
            specialist = network_registry.get_agent(specialist_id)
            if not specialist:
                return {
                    "success": False,
                    "error": f"Specialist {specialist_id} not found",
                    "result": None
                }
            
            if not specialist.is_available():
                return {
                    "success": False,
                    "error": f"Specialist {specialist_id} is not available",
                    "result": None
                }
            
            # Create collaboration request
            collaboration_id = f"consultation_{self.requesting_agent_id}_{specialist_id}_{datetime.now().timestamp()}"
            timeout = timeout_seconds or self.default_timeout
            
            # Send consultation message
            message_id = await messaging_system.send_message(
                sender_agent=self.requesting_agent_id,
                recipient_agent=specialist_id,
                message_type=NetworkCommunicationType.CONSULTATION_REQUEST,
                content={
                    "collaboration_id": collaboration_id,
                    "query": query,
                    "context": context or {},
                    "requesting_agent": self.requesting_agent_id,
                    "timestamp": datetime.now().isoformat()
                },
                priority=NetworkMessagePriority.NORMAL,
                response_expected=True
            )
            
            # Wait for response
            response = await self._wait_for_response(message_id, timeout)
            
            if response:
                return {
                    "success": True,
                    "result": response.get("consultation_result"),
                    "specialist_id": specialist_id,
                    "processing_time": response.get("processing_time", 0),
                    "metadata": response.get("metadata", {})
                }
            else:
                return {
                    "success": False,
                    "error": "Consultation timed out",
                    "result": None
                }
                
        except Exception as e:
            logger.error(f"Error coordinating consultation: {e}")
            return {
                "success": False,
                "error": str(e),
                "result": None
            }

    async def coordinate_multi_agent_workflow(self, collaboration_request: CollaborationRequest) -> CollaborationResult:
        """
        Coordinate a multi-agent workflow.
        
        Args:
            collaboration_request: Request for collaboration
            
        Returns:
            Collaboration result
        """
        try:
            start_time = datetime.now()
            logger.info(f"Coordinating multi-agent workflow: {collaboration_request.collaboration_pattern}")
            
            # Discover suitable agents
            specialists = await self.discover_specialists(
                collaboration_request.required_capabilities,
                max_results=self.max_agents_per_collaboration
            )
            
            if not specialists:
                return CollaborationResult(
                    success=False,
                    results={"error": "No suitable specialists found"},
                    participating_agents=[],
                    execution_time_seconds=0,
                    pattern_used=collaboration_request.collaboration_pattern
                )
            
            # Execute collaboration pattern
            if collaboration_request.collaboration_pattern == CollaborationPattern.PARALLEL_PROCESSING:
                results = await self._execute_parallel_workflow(specialists, collaboration_request)
            elif collaboration_request.collaboration_pattern == CollaborationPattern.SEQUENTIAL_PROCESSING:
                results = await self._execute_sequential_workflow(specialists, collaboration_request)
            elif collaboration_request.collaboration_pattern == CollaborationPattern.CONSENSUS_BUILDING:
                results = await self._execute_consensus_workflow(specialists, collaboration_request)
            else:
                # Default to consultation pattern
                results = await self._execute_consultation_workflow(specialists, collaboration_request)
            
            execution_time = (datetime.now() - start_time).total_seconds()
            
            return CollaborationResult(
                success=results.get("success", False),
                results=results,
                participating_agents=[agent.agent_id for agent in specialists],
                execution_time_seconds=execution_time,
                pattern_used=collaboration_request.collaboration_pattern,
                metadata={
                    "specialists_count": len(specialists),
                    "requesting_agent": self.requesting_agent_id,
                    "timestamp": datetime.now().isoformat()
                }
            )
            
        except Exception as e:
            logger.error(f"Error coordinating multi-agent workflow: {e}")
            return CollaborationResult(
                success=False,
                results={"error": str(e)},
                participating_agents=[],
                execution_time_seconds=0,
                pattern_used=collaboration_request.collaboration_pattern
            )

    async def _execute_parallel_workflow(self, specialists: List[NetworkAgent], 
                                       request: CollaborationRequest) -> Dict[str, Any]:
        """Execute parallel processing workflow."""
        try:
            tasks = []
            for specialist in specialists:
                task = self.coordinate_consultation(
                    specialist.agent_id,
                    request.task_description,
                    request.context,
                    request.timeout_seconds
                )
                tasks.append((specialist.agent_id, task))
            
            # Execute all tasks in parallel
            results = {}
            completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
            
            for (agent_id, _), result in zip(tasks, completed_tasks):
                if isinstance(result, Exception):
                    results[agent_id] = {"success": False, "error": str(result)}
                else:
                    results[agent_id] = result
            
            return {
                "success": True,
                "pattern": "parallel",
                "agent_results": results
            }
            
        except Exception as e:
            logger.error(f"Error in parallel workflow: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_sequential_workflow(self, specialists: List[NetworkAgent],
                                         request: CollaborationRequest) -> Dict[str, Any]:
        """Execute sequential processing workflow."""
        try:
            results = {}
            current_context = request.context or {}
            
            for specialist in specialists:
                result = await self.coordinate_consultation(
                    specialist.agent_id,
                    request.task_description,
                    current_context,
                    request.timeout_seconds
                )
                
                results[specialist.agent_id] = result
                
                # Pass successful results to next agent
                if result.get("success"):
                    current_context.update(result.get("metadata", {}))
                    current_context["previous_results"] = result.get("result")
            
            return {
                "success": True,
                "pattern": "sequential",
                "agent_results": results,
                "final_context": current_context
            }
            
        except Exception as e:
            logger.error(f"Error in sequential workflow: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_consultation_workflow(self, specialists: List[NetworkAgent],
                                           request: CollaborationRequest) -> Dict[str, Any]:
        """Execute consultation workflow with the best specialist."""
        try:
            # Use the first (best) specialist for consultation
            if specialists:
                specialist = specialists[0]
                result = await self.coordinate_consultation(
                    specialist.agent_id,
                    request.task_description,
                    request.context,
                    request.timeout_seconds
                )
                
                return {
                    "success": result.get("success", False),
                    "pattern": "consultation",
                    "specialist_used": specialist.agent_id,
                    "result": result
                }
            else:
                return {"success": False, "error": "No specialists available"}
                
        except Exception as e:
            logger.error(f"Error in consultation workflow: {e}")
            return {"success": False, "error": str(e)}

    async def _execute_consensus_workflow(self, specialists: List[NetworkAgent],
                                        request: CollaborationRequest) -> Dict[str, Any]:
        """Execute consensus building workflow."""
        try:
            # This is a simplified consensus implementation
            # In a full implementation, this would use the consensus mechanisms
            
            # Get input from all specialists
            specialist_inputs = []
            for specialist in specialists[:3]:  # Limit to 3 for consensus
                result = await self.coordinate_consultation(
                    specialist.agent_id,
                    request.task_description,
                    request.context,
                    request.timeout_seconds
                )
                
                if result.get("success"):
                    specialist_inputs.append({
                        "agent_id": specialist.agent_id,
                        "input": result.get("result"),
                        "confidence": result.get("metadata", {}).get("confidence", 0.5)
                    })
            
            # Simple consensus: use the input with highest confidence
            if specialist_inputs:
                best_input = max(specialist_inputs, key=lambda x: x["confidence"])
                return {
                    "success": True,
                    "pattern": "consensus",
                    "consensus_result": best_input["input"],
                    "chosen_agent": best_input["agent_id"],
                    "all_inputs": specialist_inputs
                }
            else:
                return {"success": False, "error": "No valid inputs for consensus"}
                
        except Exception as e:
            logger.error(f"Error in consensus workflow: {e}")
            return {"success": False, "error": str(e)}

    async def _wait_for_response(self, message_id: str, timeout_seconds: int) -> Optional[Dict[str, Any]]:
        """Wait for a response to a message."""
        # This is a placeholder - in a real implementation, this would wait for
        # the actual response from the messaging system
        try:
            await asyncio.sleep(min(timeout_seconds, 2))  # Simulate processing time
            
            # Return a mock response for now
            return {
                "consultation_result": "Mock consultation result",
                "processing_time": 1.5,
                "metadata": {"confidence": 0.8}
            }
            
        except asyncio.TimeoutError:
            return None

    def get_collaboration_stats(self) -> Dict[str, Any]:
        """Get collaboration statistics."""
        total_collaborations = len(self.collaboration_history)
        successful_collaborations = len([c for c in self.collaboration_history if c.success])
        
        pattern_usage = {}
        for collab in self.collaboration_history:
            pattern = collab.pattern_used.value
            pattern_usage[pattern] = pattern_usage.get(pattern, 0) + 1
        
        return {
            "requesting_agent": self.requesting_agent_id,
            "total_collaborations": total_collaborations,
            "successful_collaborations": successful_collaborations,
            "success_rate": (successful_collaborations / total_collaborations * 100) if total_collaborations > 0 else 0,
            "active_collaborations": len(self.active_collaborations),
            "pattern_usage": pattern_usage
        }
