"""
Network Collaboration Tools

This module provides tools for agents to collaborate with other agents in the network.
These tools enable user-selected agents to leverage the network as a resource while
maintaining user control over the collaboration process.
"""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from dataclasses import dataclass

from ..core.agent_registry import NetworkAgent, network_registry
from ..core.agent_discovery import discovery_service, DiscoveryRequest, DiscoveryStrategy
from ..communication.messaging_system import (
    messaging_system, 
    NetworkCommunicationType, 
    NetworkMessagePriority
)

logger = logging.getLogger(__name__)


@dataclass
class CollaborationRequest:
    """Represents a collaboration request between agents."""
    request_id: str
    requesting_agent: str
    target_agent: str
    collaboration_type: str
    query: str
    context: Dict[str, Any]
    priority: NetworkMessagePriority = NetworkMessagePriority.NORMAL
    timeout_seconds: int = 30
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


@dataclass
class CollaborationResponse:
    """Response from a collaboration request."""
    request_id: str
    responding_agent: str
    success: bool
    result: str
    metadata: Optional[Dict[str, Any]] = None
    processing_time_seconds: Optional[float] = None
    created_at: datetime = None

    def __post_init__(self):
        if self.created_at is None:
            self.created_at = datetime.now()


class NetworkCollaborationTools:
    """
    Tools for agents to collaborate with the network.
    
    This class provides methods for:
    - Discovering suitable specialists
    - Consulting with other agents
    - Delegating tasks to specialists
    - Coordinating multi-agent workflows
    """

    def __init__(self, agent_id: str):
        """
        Initialize collaboration tools for an agent.
        
        Args:
            agent_id: ID of the agent using these tools
        """
        self.agent_id = agent_id
        self.active_collaborations: Dict[str, CollaborationRequest] = {}
        self.collaboration_history: List[CollaborationResponse] = []
        
        # Configuration
        self.default_timeout = 30  # seconds
        self.max_concurrent_collaborations = 5
        self.retry_attempts = 2
        
        logger.info(f"NetworkCollaborationTools initialized for agent {agent_id}")

    async def find_specialist(self, required_capability: str, 
                            exclude_agents: Optional[List[str]] = None,
                            min_confidence: float = 0.7) -> Optional[NetworkAgent]:
        """
        Find the best specialist for a specific capability.
        
        Args:
            required_capability: The capability needed
            exclude_agents: List of agent IDs to exclude from search
            min_confidence: Minimum confidence level required
            
        Returns:
            NetworkAgent if found, None otherwise
        """
        try:
            logger.debug(f"Agent {self.agent_id} searching for specialist with capability: {required_capability}")
            
            # Create discovery request
            discovery_request = DiscoveryRequest(
                requesting_agent=self.agent_id,
                required_capabilities=[required_capability],
                exclude_agents=exclude_agents or [],
                min_confidence_level=min_confidence,
                max_results=1,
                strategy=DiscoveryStrategy.PERFORMANCE_BASED
            )
            
            # Discover agents
            discovery_results = await discovery_service.discover_agents(discovery_request)
            
            if discovery_results:
                specialist = discovery_results[0].agent
                logger.info(f"Found specialist {specialist.agent_id} for capability {required_capability}")
                return specialist
            else:
                logger.warning(f"No specialist found for capability {required_capability}")
                return None
                
        except Exception as e:
            logger.error(f"Error finding specialist for {required_capability}: {e}")
            return None

    async def consult_specialist(self, specialist: NetworkAgent, query: str,
                               context: Optional[Dict[str, Any]] = None,
                               timeout_seconds: Optional[int] = None) -> str:
        """
        Consult with a specialist agent.
        
        Args:
            specialist: The specialist agent to consult
            query: The query or task for the specialist
            context: Additional context for the consultation
            timeout_seconds: Custom timeout for this consultation
            
        Returns:
            Response from the specialist
        """
        try:
            logger.info(f"Agent {self.agent_id} consulting with specialist {specialist.agent_id}")
            
            # Check if we're at max concurrent collaborations
            if len(self.active_collaborations) >= self.max_concurrent_collaborations:
                raise Exception("Maximum concurrent collaborations reached")
            
            # Create collaboration request
            request_id = f"{self.agent_id}_{specialist.agent_id}_{datetime.now().timestamp()}"
            timeout = timeout_seconds or self.default_timeout
            
            collaboration_request = CollaborationRequest(
                request_id=request_id,
                requesting_agent=self.agent_id,
                target_agent=specialist.agent_id,
                collaboration_type="consultation",
                query=query,
                context=context or {},
                timeout_seconds=timeout
            )
            
            # Store active collaboration
            self.active_collaborations[request_id] = collaboration_request
            
            try:
                # Send consultation message
                message_id = await messaging_system.send_message(
                    sender_agent=self.agent_id,
                    recipient_agent=specialist.agent_id,
                    message_type=NetworkCommunicationType.CONSULTATION_REQUEST,
                    content={
                        "request_id": request_id,
                        "query": query,
                        "context": context or {},
                        "requesting_agent_info": {
                            "agent_id": self.agent_id,
                            "timestamp": datetime.now().isoformat()
                        }
                    },
                    priority=NetworkMessagePriority.NORMAL,
                    response_expected=True
                )
                
                # Wait for response with timeout
                response = await self._wait_for_collaboration_response(
                    request_id, timeout
                )
                
                if response and response.success:
                    logger.info(f"Successful consultation with {specialist.agent_id}")
                    return response.result
                else:
                    error_msg = f"Consultation with {specialist.agent_id} failed"
                    if response:
                        error_msg += f": {response.result}"
                    logger.error(error_msg)
                    return f"Consultation failed: {error_msg}"
                    
            finally:
                # Clean up active collaboration
                if request_id in self.active_collaborations:
                    del self.active_collaborations[request_id]
                    
        except asyncio.TimeoutError:
            logger.error(f"Consultation with {specialist.agent_id} timed out")
            return "Consultation timed out. The specialist may be busy."
        except Exception as e:
            logger.error(f"Error consulting with {specialist.agent_id}: {e}")
            return f"Consultation error: {str(e)}"

    async def delegate_task(self, specialist: NetworkAgent, task_description: str,
                          task_data: Dict[str, Any], 
                          timeout_seconds: Optional[int] = None) -> Dict[str, Any]:
        """
        Delegate a task to a specialist agent.
        
        Args:
            specialist: The specialist agent to delegate to
            task_description: Description of the task
            task_data: Data needed for the task
            timeout_seconds: Custom timeout for this delegation
            
        Returns:
            Task result from the specialist
        """
        try:
            logger.info(f"Agent {self.agent_id} delegating task to {specialist.agent_id}")
            
            # Create delegation request
            request_id = f"delegate_{self.agent_id}_{specialist.agent_id}_{datetime.now().timestamp()}"
            timeout = timeout_seconds or (self.default_timeout * 2)  # Longer timeout for tasks
            
            collaboration_request = CollaborationRequest(
                request_id=request_id,
                requesting_agent=self.agent_id,
                target_agent=specialist.agent_id,
                collaboration_type="delegation",
                query=task_description,
                context=task_data,
                timeout_seconds=timeout
            )
            
            self.active_collaborations[request_id] = collaboration_request
            
            try:
                # Send delegation message
                message_id = await messaging_system.send_message(
                    sender_agent=self.agent_id,
                    recipient_agent=specialist.agent_id,
                    message_type=NetworkCommunicationType.DELEGATION,
                    content={
                        "request_id": request_id,
                        "task_description": task_description,
                        "task_data": task_data,
                        "requesting_agent": self.agent_id,
                        "delegation_timestamp": datetime.now().isoformat()
                    },
                    priority=NetworkMessagePriority.HIGH,
                    response_expected=True
                )
                
                # Wait for task completion
                response = await self._wait_for_collaboration_response(
                    request_id, timeout
                )
                
                if response and response.success:
                    logger.info(f"Task delegation to {specialist.agent_id} completed successfully")
                    return {
                        "success": True,
                        "result": response.result,
                        "metadata": response.metadata or {},
                        "specialist_id": specialist.agent_id,
                        "processing_time": response.processing_time_seconds
                    }
                else:
                    error_msg = f"Task delegation to {specialist.agent_id} failed"
                    if response:
                        error_msg += f": {response.result}"
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "error": error_msg,
                        "specialist_id": specialist.agent_id
                    }
                    
            finally:
                if request_id in self.active_collaborations:
                    del self.active_collaborations[request_id]
                    
        except Exception as e:
            logger.error(f"Error delegating task to {specialist.agent_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "specialist_id": specialist.agent_id
            }

    async def coordinate_multi_agent_task(self, task_description: str,
                                        required_capabilities: List[str],
                                        task_data: Dict[str, Any],
                                        coordination_strategy: str = "sequential") -> Dict[str, Any]:
        """
        Coordinate a task that requires multiple agents.
        
        Args:
            task_description: Description of the overall task
            required_capabilities: List of capabilities needed
            task_data: Data for the task
            coordination_strategy: How to coordinate agents ("sequential" or "parallel")
            
        Returns:
            Combined results from all agents
        """
        try:
            logger.info(f"Agent {self.agent_id} coordinating multi-agent task with {len(required_capabilities)} capabilities")
            
            # Find specialists for each capability
            specialists = {}
            for capability in required_capabilities:
                specialist = await self.find_specialist(capability)
                if specialist:
                    specialists[capability] = specialist
                else:
                    logger.warning(f"No specialist found for capability: {capability}")
            
            if not specialists:
                return {
                    "success": False,
                    "error": "No specialists found for required capabilities",
                    "required_capabilities": required_capabilities
                }
            
            # Execute coordination strategy
            if coordination_strategy == "parallel":
                results = await self._execute_parallel_coordination(
                    specialists, task_description, task_data
                )
            else:  # sequential
                results = await self._execute_sequential_coordination(
                    specialists, task_description, task_data
                )
            
            return {
                "success": True,
                "results": results,
                "specialists_used": list(specialists.keys()),
                "coordination_strategy": coordination_strategy
            }
            
        except Exception as e:
            logger.error(f"Error in multi-agent coordination: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _wait_for_collaboration_response(self, request_id: str, 
                                             timeout_seconds: int) -> Optional[CollaborationResponse]:
        """Wait for a collaboration response."""
        # This is a placeholder - in a real implementation, this would wait for
        # the actual response from the messaging system
        try:
            await asyncio.sleep(min(timeout_seconds, 1))  # Simulate processing time
            
            # For now, return a mock successful response
            return CollaborationResponse(
                request_id=request_id,
                responding_agent="mock_specialist",
                success=True,
                result="Mock collaboration result",
                processing_time_seconds=1.0
            )
            
        except asyncio.TimeoutError:
            return None

    async def _execute_parallel_coordination(self, specialists: Dict[str, NetworkAgent],
                                           task_description: str, 
                                           task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute parallel coordination strategy."""
        tasks = []
        for capability, specialist in specialists.items():
            task = self.delegate_task(
                specialist, 
                f"{task_description} (capability: {capability})",
                task_data
            )
            tasks.append((capability, task))
        
        results = {}
        completed_tasks = await asyncio.gather(*[task for _, task in tasks], return_exceptions=True)
        
        for (capability, _), result in zip(tasks, completed_tasks):
            if isinstance(result, Exception):
                results[capability] = {"success": False, "error": str(result)}
            else:
                results[capability] = result
        
        return results

    async def _execute_sequential_coordination(self, specialists: Dict[str, NetworkAgent],
                                             task_description: str,
                                             task_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute sequential coordination strategy."""
        results = {}
        current_data = task_data.copy()
        
        for capability, specialist in specialists.items():
            result = await self.delegate_task(
                specialist,
                f"{task_description} (capability: {capability})",
                current_data
            )
            
            results[capability] = result
            
            # Pass successful results to next agent
            if result.get("success"):
                current_data.update(result.get("metadata", {}))
        
        return results

    def get_collaboration_stats(self) -> Dict[str, Any]:
        """Get collaboration statistics for this agent."""
        total_collaborations = len(self.collaboration_history)
        successful_collaborations = len([r for r in self.collaboration_history if r.success])
        
        return {
            "total_collaborations": total_collaborations,
            "successful_collaborations": successful_collaborations,
            "success_rate": (successful_collaborations / total_collaborations * 100) if total_collaborations > 0 else 0,
            "active_collaborations": len(self.active_collaborations),
            "agent_id": self.agent_id
        }
