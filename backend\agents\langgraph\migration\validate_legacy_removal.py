#!/usr/bin/env python3
"""
Legacy Removal Validation Script

This script validates that all legacy components have been successfully removed
and the system is running purely on the unified persona system.
"""

import os
import sys
import logging
from pathlib import Path
from typing import List, Dict, Any
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class LegacyRemovalValidator:
    """Validates that all legacy components have been removed."""
    
    def __init__(self):
        """Initialize the validator."""
        self.backend_path = Path(__file__).parent.parent.parent.parent
        self.agents_path = self.backend_path / "agents"
        self.config_path = self.backend_path / "agents" / "langgraph" / "config"
        
        # Legacy directories that should be removed
        self.legacy_directories = [
            "analysis_agent",
            "marketing_agent", 
            "concierge_agent",
            "classification"
        ]
        
        # Legacy files that should be removed
        self.legacy_files = [
            "composable_agent.py",
            "concierge.py"
        ]
        
        # Legacy terms that should not appear in configurations
        self.legacy_terms = [
            "legacy_class",
            "legacy_compatibility",
            "enable_legacy_adapter",
            "migration_mode",
            "composable-analysis-ai",
            "composable-marketing-ai",
            "classification-agent"
        ]
        
        logger.info("Initialized LegacyRemovalValidator")
    
    def validate_directory_removal(self) -> Dict[str, Any]:
        """Validate that legacy agent directories have been removed."""
        logger.info("Validating legacy directory removal...")
        
        results = {
            "directories_removed": True,
            "remaining_directories": [],
            "details": []
        }
        
        for legacy_dir in self.legacy_directories:
            dir_path = self.agents_path / legacy_dir
            if dir_path.exists():
                results["directories_removed"] = False
                results["remaining_directories"].append(str(dir_path))
                results["details"].append(f"Legacy directory still exists: {dir_path}")
                logger.error(f"❌ Legacy directory found: {dir_path}")
            else:
                results["details"].append(f"✅ Legacy directory removed: {legacy_dir}")
                logger.info(f"✅ Legacy directory removed: {legacy_dir}")
        
        return results
    
    def validate_file_removal(self) -> Dict[str, Any]:
        """Validate that legacy agent files have been removed."""
        logger.info("Validating legacy file removal...")
        
        results = {
            "files_removed": True,
            "remaining_files": [],
            "details": []
        }
        
        # Search for legacy files in the agents directory
        for root, dirs, files in os.walk(self.agents_path):
            for file in files:
                if any(legacy_file in file for legacy_file in self.legacy_files):
                    file_path = Path(root) / file
                    results["files_removed"] = False
                    results["remaining_files"].append(str(file_path))
                    results["details"].append(f"Legacy file still exists: {file_path}")
                    logger.error(f"❌ Legacy file found: {file_path}")
        
        if results["files_removed"]:
            results["details"].append("✅ All legacy agent files removed")
            logger.info("✅ All legacy agent files removed")
        
        return results
    
    def validate_configuration_cleanup(self) -> Dict[str, Any]:
        """Validate that legacy terms have been removed from configurations."""
        logger.info("Validating configuration cleanup...")
        
        results = {
            "configurations_clean": True,
            "legacy_references": [],
            "details": []
        }
        
        # Check YAML and JSON files in config directory
        config_files = list(self.config_path.glob("**/*.yaml")) + list(self.config_path.glob("**/*.json"))
        
        for config_file in config_files:
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                found_legacy_terms = []
                for term in self.legacy_terms:
                    if term in content:
                        found_legacy_terms.append(term)
                
                if found_legacy_terms:
                    results["configurations_clean"] = False
                    results["legacy_references"].append({
                        "file": str(config_file),
                        "terms": found_legacy_terms
                    })
                    results["details"].append(f"Legacy terms in {config_file.name}: {found_legacy_terms}")
                    logger.error(f"❌ Legacy terms found in {config_file.name}: {found_legacy_terms}")
                else:
                    results["details"].append(f"✅ Configuration clean: {config_file.name}")
                    logger.info(f"✅ Configuration clean: {config_file.name}")
                    
            except Exception as e:
                logger.warning(f"Could not read {config_file}: {e}")
        
        return results
    
    def validate_unified_system_status(self) -> Dict[str, Any]:
        """Validate that the unified system is properly configured."""
        logger.info("Validating unified system status...")
        
        results = {
            "unified_system_ready": True,
            "persona_configs_exist": True,
            "registry_updated": True,
            "details": []
        }
        
        # Check that persona configurations exist
        required_personas = ["analysis", "marketing", "concierge", "classification"]
        personas_path = self.config_path / "personas"
        
        for persona in required_personas:
            yaml_file = personas_path / f"{persona}.yaml"
            if yaml_file.exists():
                results["details"].append(f"✅ Persona config exists: {persona}.yaml")
                logger.info(f"✅ Persona config exists: {persona}.yaml")
            else:
                results["persona_configs_exist"] = False
                results["unified_system_ready"] = False
                results["details"].append(f"❌ Missing persona config: {persona}.yaml")
                logger.error(f"❌ Missing persona config: {persona}.yaml")
        
        # Check persona registry
        registry_file = self.config_path / "persona_registry.yaml"
        if registry_file.exists():
            try:
                with open(registry_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if "migration_status: \"completed\"" in content:
                    results["details"].append("✅ Persona registry shows migration completed")
                    logger.info("✅ Persona registry shows migration completed")
                else:
                    results["registry_updated"] = False
                    results["unified_system_ready"] = False
                    results["details"].append("❌ Persona registry not updated to completed status")
                    logger.error("❌ Persona registry not updated to completed status")
                    
            except Exception as e:
                results["registry_updated"] = False
                results["unified_system_ready"] = False
                results["details"].append(f"❌ Could not read persona registry: {e}")
                logger.error(f"❌ Could not read persona registry: {e}")
        else:
            results["registry_updated"] = False
            results["unified_system_ready"] = False
            results["details"].append("❌ Persona registry file not found")
            logger.error("❌ Persona registry file not found")
        
        return results
    
    def run_complete_validation(self) -> Dict[str, Any]:
        """Run complete legacy removal validation."""
        logger.info("=" * 80)
        logger.info("🔍 LEGACY REMOVAL VALIDATION")
        logger.info("=" * 80)
        
        validation_results = {
            "validation_passed": True,
            "directory_removal": {},
            "file_removal": {},
            "configuration_cleanup": {},
            "unified_system_status": {},
            "summary": {}
        }
        
        try:
            # Validate directory removal
            logger.info("\n📁 Validating Directory Removal")
            logger.info("-" * 40)
            validation_results["directory_removal"] = self.validate_directory_removal()
            
            # Validate file removal
            logger.info("\n📄 Validating File Removal")
            logger.info("-" * 40)
            validation_results["file_removal"] = self.validate_file_removal()
            
            # Validate configuration cleanup
            logger.info("\n⚙️ Validating Configuration Cleanup")
            logger.info("-" * 40)
            validation_results["configuration_cleanup"] = self.validate_configuration_cleanup()
            
            # Validate unified system status
            logger.info("\n🎯 Validating Unified System Status")
            logger.info("-" * 40)
            validation_results["unified_system_status"] = self.validate_unified_system_status()
            
            # Determine overall validation status
            validation_passed = (
                validation_results["directory_removal"]["directories_removed"] and
                validation_results["file_removal"]["files_removed"] and
                validation_results["configuration_cleanup"]["configurations_clean"] and
                validation_results["unified_system_status"]["unified_system_ready"]
            )
            
            validation_results["validation_passed"] = validation_passed
            
            # Generate summary
            validation_results["summary"] = {
                "directories_removed": validation_results["directory_removal"]["directories_removed"],
                "files_removed": validation_results["file_removal"]["files_removed"],
                "configurations_clean": validation_results["configuration_cleanup"]["configurations_clean"],
                "unified_system_ready": validation_results["unified_system_status"]["unified_system_ready"],
                "overall_status": "PASSED" if validation_passed else "FAILED"
            }
            
            # Log final results
            logger.info("\n" + "=" * 80)
            if validation_passed:
                logger.info("🎉 LEGACY REMOVAL VALIDATION PASSED!")
                logger.info("✅ All legacy components successfully removed")
                logger.info("✅ Unified persona system is ready")
                logger.info("✅ System is running in unified-only mode")
            else:
                logger.error("❌ LEGACY REMOVAL VALIDATION FAILED!")
                logger.error("❌ Some legacy components remain")
                logger.error("❌ Manual cleanup required")
            
            logger.info("=" * 80)
            
            return validation_results
            
        except Exception as e:
            logger.error(f"Critical error during validation: {e}")
            validation_results["validation_passed"] = False
            validation_results["error"] = str(e)
            return validation_results


def main():
    """Main execution function."""
    try:
        validator = LegacyRemovalValidator()
        results = validator.run_complete_validation()
        
        # Save results
        results_file = "legacy_removal_validation.json"
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"Validation results saved to: {results_file}")
        
        # Exit with appropriate code
        sys.exit(0 if results["validation_passed"] else 1)
        
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
