"""
Unified Conversation Management Tool for MCP System.

This tool provides centralized conversation management that all agents can use,
eliminating the need for agents to control their own conversation flow.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid

from .base import MC<PERSON><PERSON>ool
from ...langgraph.states.unified_state import UnifiedDatageniusState, MessageType, ConversationMode

logger = logging.getLogger(__name__)


class ConversationAction(str, Enum):
    """Types of conversation actions."""
    START_CONVERSATION = "start_conversation"
    ADD_MESSAGE = "add_message"
    GET_HISTORY = "get_history"
    UPDATE_CONTEXT = "update_context"
    SWITCH_AGENT = "switch_agent"
    END_CONVERSATION = "end_conversation"
    GET_STATE = "get_state"
    SET_MODE = "set_mode"


class ConversationStatus(str, Enum):
    """Conversation status types."""
    ACTIVE = "active"
    PAUSED = "paused"
    ENDED = "ended"
    ERROR = "error"


@dataclass
class ConversationContext:
    """Context for a conversation."""
    conversation_id: str
    user_id: str
    current_agent: Optional[str] = None
    selected_agent: Optional[str] = None
    mode: ConversationMode = ConversationMode.CONVERSATION
    status: ConversationStatus = ConversationStatus.ACTIVE
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    business_profile_id: Optional[str] = None
    session_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConversationMessage:
    """A message in a conversation."""
    id: str
    content: str
    sender_type: MessageType
    sender_id: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    in_response_to: Optional[str] = None
    attachments: List[Dict[str, Any]] = field(default_factory=list)


class ConversationManagerTool(MCPTool):
    """
    Unified conversation management tool accessible to all agents.
    
    This tool centralizes conversation flow control, eliminating the need
    for individual agents to manage their own conversation state.
    """

    def __init__(self):
        """Initialize the conversation manager tool."""
        super().__init__()
        
        # Conversation storage
        self.conversations: Dict[str, ConversationContext] = {}
        self.messages: Dict[str, List[ConversationMessage]] = {}
        
        # Configuration
        self.max_conversations = 10000
        self.max_messages_per_conversation = 1000
        self.conversation_timeout = timedelta(hours=24)
        self.cleanup_interval = timedelta(hours=1)
        
        # Performance tracking
        self.metrics = {
            "conversations_created": 0,
            "messages_processed": 0,
            "agent_switches": 0,
            "errors": 0
        }
        
        # Start background cleanup task
        self._cleanup_task = None
        self._start_cleanup_task()

    @property
    def name(self) -> str:
        """Get the name of the tool."""
        return "conversation_manager"

    @property
    def description(self) -> str:
        """Get the description of the tool."""
        return "Centralized conversation management for all agents"

    @property
    def definition(self) -> Dict[str, Any]:
        """Get the tool definition in MCP format."""
        return self.get_tool_schema()

    async def _initialize(self, config: Dict[str, Any]) -> None:
        """Additional initialization for the conversation manager."""
        # Update configuration if provided
        if config:
            self.max_conversations = config.get("max_conversations", self.max_conversations)
            self.max_messages_per_conversation = config.get("max_messages_per_conversation", self.max_messages_per_conversation)
            self.conversation_timeout = timedelta(hours=config.get("conversation_timeout_hours", 24))
            self.cleanup_interval = timedelta(hours=config.get("cleanup_interval_hours", 1))

    async def execute(self, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute conversation management action.
        
        Args:
            parameters: Action parameters including:
                - action: ConversationAction to perform
                - conversation_id: Conversation identifier
                - user_id: User identifier
                - Additional action-specific parameters
                
        Returns:
            Dict containing action result and conversation state
        """
        try:
            action = ConversationAction(arguments.get("action"))
            conversation_id = arguments.get("conversation_id")
            user_id = arguments.get("user_id")

            # Validate required parameters
            if not conversation_id or not user_id:
                raise ValueError("conversation_id and user_id are required")

            # Route to appropriate handler
            if action == ConversationAction.START_CONVERSATION:
                return await self._start_conversation(conversation_id, user_id, arguments)
            elif action == ConversationAction.ADD_MESSAGE:
                return await self._add_message(conversation_id, arguments)
            elif action == ConversationAction.GET_HISTORY:
                return await self._get_history(conversation_id, arguments)
            elif action == ConversationAction.UPDATE_CONTEXT:
                return await self._update_context(conversation_id, arguments)
            elif action == ConversationAction.SWITCH_AGENT:
                return await self._switch_agent(conversation_id, arguments)
            elif action == ConversationAction.END_CONVERSATION:
                return await self._end_conversation(conversation_id)
            elif action == ConversationAction.GET_STATE:
                return await self._get_state(conversation_id)
            elif action == ConversationAction.SET_MODE:
                return await self._set_mode(conversation_id, arguments)
            else:
                raise ValueError(f"Unknown action: {action}")
                
        except Exception as e:
            self.metrics["errors"] += 1
            logger.error(f"Error in conversation manager: {e}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "error_type": type(e).__name__
            }

    async def _start_conversation(
        self, 
        conversation_id: str, 
        user_id: str, 
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Start a new conversation or resume existing one."""
        if conversation_id in self.conversations:
            # Resume existing conversation
            context = self.conversations[conversation_id]
            context.status = ConversationStatus.ACTIVE
            context.updated_at = datetime.now()
        else:
            # Create new conversation
            context = ConversationContext(
                conversation_id=conversation_id,
                user_id=user_id,
                current_agent=parameters.get("initial_agent"),
                selected_agent=parameters.get("selected_agent"),
                mode=ConversationMode(parameters.get("mode", ConversationMode.CONVERSATION.value)),
                business_profile_id=parameters.get("business_profile_id"),
                metadata=parameters.get("metadata", {})
            )
            
            self.conversations[conversation_id] = context
            self.messages[conversation_id] = []
            self.metrics["conversations_created"] += 1
        
        return {
            "success": True,
            "conversation_id": conversation_id,
            "context": self._serialize_context(context),
            "message": "Conversation started successfully"
        }

    async def _add_message(self, conversation_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Add a message to the conversation."""
        if conversation_id not in self.conversations:
            raise ValueError(f"Conversation {conversation_id} not found")
        
        message = ConversationMessage(
            id=parameters.get("message_id", str(uuid.uuid4())),
            content=parameters.get("content", ""),
            sender_type=MessageType(parameters.get("sender_type", MessageType.USER.value)),
            sender_id=parameters.get("sender_id"),
            metadata=parameters.get("metadata", {}),
            in_response_to=parameters.get("in_response_to"),
            attachments=parameters.get("attachments", [])
        )
        
        # Add message to conversation
        if conversation_id not in self.messages:
            self.messages[conversation_id] = []
        
        self.messages[conversation_id].append(message)
        
        # Trim messages if too many
        if len(self.messages[conversation_id]) > self.max_messages_per_conversation:
            self.messages[conversation_id] = self.messages[conversation_id][-self.max_messages_per_conversation:]
        
        # Update conversation context
        self.conversations[conversation_id].updated_at = datetime.now()
        self.metrics["messages_processed"] += 1
        
        return {
            "success": True,
            "message_id": message.id,
            "message_count": len(self.messages[conversation_id]),
            "message": "Message added successfully"
        }

    async def _get_history(self, conversation_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Get conversation history."""
        if conversation_id not in self.conversations:
            return {
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }
        
        messages = self.messages.get(conversation_id, [])
        limit = parameters.get("limit", 50)
        offset = parameters.get("offset", 0)
        
        # Apply pagination
        paginated_messages = messages[offset:offset + limit] if limit > 0 else messages[offset:]
        
        return {
            "success": True,
            "messages": [self._serialize_message(msg) for msg in paginated_messages],
            "total_messages": len(messages),
            "context": self._serialize_context(self.conversations[conversation_id])
        }

    async def _update_context(self, conversation_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Update conversation context."""
        if conversation_id not in self.conversations:
            raise ValueError(f"Conversation {conversation_id} not found")
        
        context = self.conversations[conversation_id]
        
        # Update allowed fields
        if "current_agent" in parameters:
            context.current_agent = parameters["current_agent"]
        if "selected_agent" in parameters:
            context.selected_agent = parameters["selected_agent"]
        if "mode" in parameters:
            context.mode = ConversationMode(parameters["mode"])
        if "status" in parameters:
            context.status = ConversationStatus(parameters["status"])
        if "metadata" in parameters:
            context.metadata.update(parameters["metadata"])
        if "business_profile_id" in parameters:
            context.business_profile_id = parameters["business_profile_id"]
        if "session_data" in parameters:
            context.session_data.update(parameters["session_data"])
        
        context.updated_at = datetime.now()
        
        return {
            "success": True,
            "context": self._serialize_context(context),
            "message": "Context updated successfully"
        }

    async def _switch_agent(self, conversation_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Switch to a different agent."""
        if conversation_id not in self.conversations:
            raise ValueError(f"Conversation {conversation_id} not found")
        
        new_agent = parameters.get("new_agent")
        if not new_agent:
            raise ValueError("new_agent parameter is required")
        
        context = self.conversations[conversation_id]
        old_agent = context.current_agent
        
        context.current_agent = new_agent
        context.selected_agent = new_agent
        context.mode = ConversationMode.AGENT_SWITCH
        context.updated_at = datetime.now()
        
        # Add metadata about the switch
        context.metadata["last_agent_switch"] = {
            "from": old_agent,
            "to": new_agent,
            "timestamp": datetime.now().isoformat(),
            "reason": parameters.get("reason", "user_request")
        }
        
        self.metrics["agent_switches"] += 1
        
        return {
            "success": True,
            "old_agent": old_agent,
            "new_agent": new_agent,
            "context": self._serialize_context(context),
            "message": f"Switched from {old_agent} to {new_agent}"
        }

    def _serialize_context(self, context: ConversationContext) -> Dict[str, Any]:
        """Serialize conversation context for JSON response."""
        return {
            "conversation_id": context.conversation_id,
            "user_id": context.user_id,
            "current_agent": context.current_agent,
            "selected_agent": context.selected_agent,
            "mode": context.mode.value,
            "status": context.status.value,
            "created_at": context.created_at.isoformat(),
            "updated_at": context.updated_at.isoformat(),
            "metadata": context.metadata,
            "business_profile_id": context.business_profile_id,
            "session_data": context.session_data
        }

    def _serialize_message(self, message: ConversationMessage) -> Dict[str, Any]:
        """Serialize conversation message for JSON response."""
        return {
            "id": message.id,
            "content": message.content,
            "sender_type": message.sender_type.value,
            "sender_id": message.sender_id,
            "timestamp": message.timestamp.isoformat(),
            "metadata": message.metadata,
            "in_response_to": message.in_response_to,
            "attachments": message.attachments
        }

    async def _end_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """End a conversation."""
        if conversation_id not in self.conversations:
            raise ValueError(f"Conversation {conversation_id} not found")

        context = self.conversations[conversation_id]
        context.status = ConversationStatus.ENDED
        context.updated_at = datetime.now()

        return {
            "success": True,
            "conversation_id": conversation_id,
            "message": "Conversation ended successfully"
        }

    async def _get_state(self, conversation_id: str) -> Dict[str, Any]:
        """Get current conversation state."""
        if conversation_id not in self.conversations:
            return {
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }

        context = self.conversations[conversation_id]
        messages = self.messages.get(conversation_id, [])

        return {
            "success": True,
            "context": self._serialize_context(context),
            "message_count": len(messages),
            "last_message_time": messages[-1].timestamp.isoformat() if messages else None,
            "metrics": self.metrics
        }

    async def _set_mode(self, conversation_id: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Set conversation mode."""
        if conversation_id not in self.conversations:
            raise ValueError(f"Conversation {conversation_id} not found")

        new_mode = ConversationMode(parameters.get("mode"))
        context = self.conversations[conversation_id]
        old_mode = context.mode

        context.mode = new_mode
        context.updated_at = datetime.now()

        return {
            "success": True,
            "old_mode": old_mode.value,
            "new_mode": new_mode.value,
            "message": f"Mode changed from {old_mode.value} to {new_mode.value}"
        }

    def _start_cleanup_task(self):
        """Start background cleanup task."""
        async def cleanup_task():
            while True:
                try:
                    await asyncio.sleep(self.cleanup_interval.total_seconds())
                    await self._cleanup_old_conversations()
                except Exception as e:
                    logger.error(f"Error in cleanup task: {e}")

        if self._cleanup_task is None:
            try:
                self._cleanup_task = asyncio.create_task(cleanup_task())
            except RuntimeError:
                # No event loop running, cleanup will be manual
                logger.info("No event loop for cleanup task, will cleanup manually")

    async def _cleanup_old_conversations(self):
        """Clean up old conversations."""
        now = datetime.now()
        expired_conversations = []

        for conv_id, context in self.conversations.items():
            if now - context.updated_at > self.conversation_timeout:
                expired_conversations.append(conv_id)

        for conv_id in expired_conversations:
            del self.conversations[conv_id]
            if conv_id in self.messages:
                del self.messages[conv_id]

        if expired_conversations:
            logger.info(f"Cleaned up {len(expired_conversations)} expired conversations")

    def get_tool_schema(self) -> Dict[str, Any]:
        """Get the MCP tool schema for this conversation manager."""
        return {
            "name": self.name,
            "description": self.description,
            "inputSchema": {
                "type": "object",
                "properties": {
                    "action": {
                        "type": "string",
                        "enum": [action.value for action in ConversationAction],
                        "description": "The conversation action to perform"
                    },
                    "conversation_id": {
                        "type": "string",
                        "description": "Unique identifier for the conversation"
                    },
                    "user_id": {
                        "type": "string",
                        "description": "Unique identifier for the user"
                    },
                    "content": {
                        "type": "string",
                        "description": "Message content (for add_message action)"
                    },
                    "sender_type": {
                        "type": "string",
                        "enum": [msg_type.value for msg_type in MessageType],
                        "description": "Type of message sender"
                    },
                    "sender_id": {
                        "type": "string",
                        "description": "ID of the message sender"
                    },
                    "metadata": {
                        "type": "object",
                        "description": "Additional metadata for the action"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "Limit for history retrieval"
                    },
                    "offset": {
                        "type": "integer",
                        "description": "Offset for history retrieval"
                    },
                    "new_agent": {
                        "type": "string",
                        "description": "New agent ID for agent switching"
                    },
                    "mode": {
                        "type": "string",
                        "enum": [mode.value for mode in ConversationMode],
                        "description": "Conversation mode"
                    }
                },
                "required": ["action", "conversation_id", "user_id"]
            }
        }
