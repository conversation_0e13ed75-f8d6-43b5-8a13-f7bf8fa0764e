"""
Check the agent registry for registered personas.

This script checks the agent registry to see what personas are registered.
"""

import os
import sys
import logging

# Add the parent directory to sys.path to allow importing from app
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.insert(0, parent_dir)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_registry():
    """
    Check the agent registry for registered personas.
    """
    try:
        # Import the agent factory
        from agents.langgraph.core.agent_factory import agent_factory

        # Get all available agents
        available_agents = agent_factory.get_available_agents()
        logger.info(f"Available agents: {available_agents}")

        # Check for specific persona (using correct consolidated ID)
        persona_id = "composable-marketing-ai"
        if persona_id in available_agents:
            logger.info(f"Agent {persona_id} is available")

            # Get the agent class
            agent_class = agent_factory.agent_classes.get(persona_id)
            logger.info(f"Agent class for {persona_id}: {agent_class}")

            # Get the configuration
            config = agent_factory.agent_configs.get(persona_id)
            logger.info(f"Configuration for {persona_id}: {config}")
        else:
            logger.error(f"Agent {persona_id} is not available")
            
        # Check persona files
        personas_dir = os.path.join(parent_dir, "personas")
        if os.path.exists(personas_dir):
            persona_files = os.listdir(personas_dir)
            logger.info(f"Persona files in {personas_dir}: {persona_files}")
        else:
            logger.error(f"Personas directory not found: {personas_dir}")
            
        # Check agent modules
        agents_dir = os.path.join(parent_dir, "agents")
        if os.path.exists(agents_dir):
            agent_files = [f for f in os.listdir(agents_dir) if f.endswith(".py") or os.path.isdir(os.path.join(agents_dir, f))]
            logger.info(f"Agent files in {agents_dir}: {agent_files}")
            
            # Check for marketing agent specifically
            marketing_dir = os.path.join(agents_dir, "marketing_agent")
            if os.path.exists(marketing_dir) and os.path.isdir(marketing_dir):
                marketing_files = os.listdir(marketing_dir)
                logger.info(f"Marketing agent files: {marketing_files}")
            else:
                logger.warning(f"Marketing agent directory not found: {marketing_dir}")
        else:
            logger.error(f"Agents directory not found: {agents_dir}")
            
    except Exception as e:
        logger.error(f"Error checking registry: {str(e)}", exc_info=True)

if __name__ == "__main__":
    check_registry()
