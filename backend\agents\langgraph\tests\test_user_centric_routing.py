"""
Test suite for user-centric LangGraph routing architecture changes.

This module tests the implementation of the user-centric routing changes
documented in workflow.md, including:
- Dynamic entry points based on selected agent
- Tool routing back to selected agent
- Simplified routing logic prioritizing user selection
- Production-ready error handling and validation
"""

import pytest
import asyncio
import logging
from unittest.mock import Mock, AsyncMock, patch
from typing import Dict, Any

from ..core.workflow_manager import WorkflowManager
from ..core.routing_constants import RoutingConstants, ValidationError, AgentNotFoundError
from ..core.routing_utils import create_routing_decision_maker, RoutingDecisionMaker
from ..states.unified_state import (
    UnifiedDatageniusState,
    create_unified_state,
    get_routing_target,
    ConversationMode
)
from ..nodes.base_agent_node import BaseAgentNode

# Configure logging for tests
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockAgentNode(BaseAgentNode):
    """Mock agent node for testing."""
    
    def __init__(self, agent_id: str):
        super().__init__(agent_id, "test_agent")
        self.execution_calls = []
    
    async def _process_message(self, state: UnifiedDatageniusState):
        """Mock message processing."""
        self.execution_calls.append(state.get("current_message", {}))
        return state


class TestRoutingConstants:
    """Test routing constants and utilities."""

    def test_format_agent_node_name(self):
        """Test agent node name formatting."""
        from ..core.routing_constants import format_agent_node_name

        assert format_agent_node_name("marketing") == "agent_marketing"
        assert format_agent_node_name("agent_marketing") == "agent_marketing"

        with pytest.raises(ValidationError):
            format_agent_node_name("")

    def test_is_concierge_agent(self):
        """Test concierge agent detection."""
        from ..core.routing_constants import is_concierge_agent

        assert is_concierge_agent("concierge") is True
        assert is_concierge_agent("marketing") is False
        assert is_concierge_agent("") is False


class TestRoutingDecisionMaker:
    """Test the routing decision maker."""

    @pytest.fixture
    def agent_nodes(self):
        """Create mock agent nodes."""
        return {
            "concierge": MockAgentNode("concierge"),
            "marketing": MockAgentNode("marketing"),
            "analysis": MockAgentNode("analysis")
        }

    @pytest.fixture
    def routing_decision_maker(self, agent_nodes):
        """Create a routing decision maker for testing."""
        return create_routing_decision_maker(agent_nodes)

    def test_determine_entry_point_with_selected_agent(self, routing_decision_maker):
        """Test entry point determination with selected agent."""
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="marketing"
        )

        entry_point = routing_decision_maker.determine_entry_point(state)
        assert entry_point == "agent_marketing"

    def test_determine_entry_point_fallback(self, routing_decision_maker):
        """Test entry point fallback to concierge."""
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation"
        )

        entry_point = routing_decision_maker.determine_entry_point(state)
        assert entry_point == "agent_concierge"

    def test_determine_tool_routing_target(self, routing_decision_maker):
        """Test tool routing target determination."""
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="marketing"
        )

        target = routing_decision_maker.determine_tool_routing_target(state)
        assert target == "agent_marketing"

    def test_validation_error_handling(self, routing_decision_maker):
        """Test validation error handling."""
        with pytest.raises(ValidationError):
            routing_decision_maker.determine_entry_point(None)

        with pytest.raises(ValidationError):
            routing_decision_maker.determine_tool_routing_target(None)


class TestUserCentricRouting:
    """Test suite for user-centric routing architecture."""

    @pytest.fixture
    def workflow_manager(self):
        """Create a workflow manager for testing."""
        manager = WorkflowManager()

        # Add mock agents
        manager.agent_nodes = {
            "concierge": MockAgentNode("concierge"),
            "marketing": MockAgentNode("marketing"),
            "analysis": MockAgentNode("analysis")
        }

        # Initialize routing decision maker
        manager.routing_decision_maker = create_routing_decision_maker(manager.agent_nodes)

        # Add mock tools
        manager.tool_nodes = {
            "data_query": Mock(),
            "web_search": Mock()
        }

        return manager
    
    @pytest.fixture
    def sample_state(self):
        """Create a sample state for testing."""
        return create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="marketing",
            conversation_mode=ConversationMode.CONVERSATION
        )
    
    def test_get_entry_point_with_selected_agent(self, workflow_manager, sample_state):
        """Test that entry point uses selected agent."""
        entry_point = workflow_manager._get_entry_point(sample_state)
        assert entry_point == "agent_marketing"
        logger.info(f"✅ Entry point correctly uses selected agent: {entry_point}")
    
    def test_get_entry_point_fallback_to_concierge(self, workflow_manager):
        """Test fallback to concierge when no selected agent."""
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation"
        )
        
        entry_point = workflow_manager._get_entry_point(state)
        assert entry_point == "agent_concierge"
        logger.info(f"✅ Entry point correctly falls back to concierge: {entry_point}")
    
    def test_route_from_tool_to_selected_agent(self, workflow_manager, sample_state):
        """Test that tools route back to selected agent."""
        route_target = workflow_manager._route_from_tool(sample_state)
        assert route_target == "agent_marketing"
        logger.info(f"✅ Tool routing correctly returns to selected agent: {route_target}")
    
    def test_route_from_tool_fallback(self, workflow_manager):
        """Test tool routing fallback when no selected agent."""
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation"
        )
        
        route_target = workflow_manager._route_from_tool(state)
        assert route_target == "agent_concierge"
        logger.info(f"✅ Tool routing correctly falls back to concierge: {route_target}")
    
    def test_simplified_routing_logic_selected_agent(self, sample_state):
        """Test simplified routing logic prioritizes selected agent."""
        target = get_routing_target(sample_state)
        assert target == "marketing"
        logger.info(f"✅ Routing logic correctly prioritizes selected agent: {target}")
    
    def test_simplified_routing_logic_no_fallback(self):
        """Test that routing logic doesn't fall back to automatic analysis."""
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation"
        )
        
        # Add routing analysis that should be ignored
        state["routing_analysis"] = {
            "target_agent": "analysis",
            "confidence": 0.9
        }
        
        target = get_routing_target(state)
        assert target is None  # Should not use routing analysis
        logger.info("✅ Routing logic correctly ignores automatic analysis")
    
    def test_command_pattern_routing(self):
        """Test that Command pattern routing still works."""
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation"
        )
        
        # Mock agent command
        mock_command = Mock()
        mock_command.goto = "analysis"
        state["agent_command"] = mock_command
        
        target = get_routing_target(state)
        assert target == "analysis"
        logger.info("✅ Command pattern routing works correctly")
    
    def test_user_selection_overrides_command(self, sample_state):
        """Test that user selection takes priority over commands."""
        # Add agent command that should be overridden
        mock_command = Mock()
        mock_command.goto = "analysis"
        sample_state["agent_command"] = mock_command
        
        target = get_routing_target(sample_state)
        assert target == "marketing"  # Should use selected agent, not command
        logger.info("✅ User selection correctly overrides command routing")


@pytest.mark.asyncio
class TestWorkflowIntegration:
    """Integration tests for the complete workflow."""
    
    @pytest.fixture
    def workflow_manager(self):
        """Create a workflow manager for integration testing."""
        manager = WorkflowManager()
        
        # Add mock agents
        manager.agent_nodes = {
            "concierge": MockAgentNode("concierge"),
            "marketing": MockAgentNode("marketing")
        }
        
        return manager
    
    async def test_workflow_starts_at_selected_agent(self, workflow_manager):
        """Test that workflow starts directly at selected agent."""
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="marketing"
        )
        
        # Test entry point determination
        entry_point = workflow_manager._get_entry_point(state)
        assert entry_point == "agent_marketing"
        
        logger.info("✅ Integration test: Workflow starts at selected agent")
    
    async def test_conversation_continuity(self, workflow_manager):
        """Test that conversation continues with selected agent."""
        state = create_unified_state(
            user_id="test_user",
            conversation_id="test_conversation",
            selected_agent="marketing"
        )
        
        # Simulate tool execution and routing back
        tool_route = workflow_manager._route_from_tool(state)
        assert tool_route == "agent_marketing"
        
        logger.info("✅ Integration test: Conversation continuity maintained")


def run_user_centric_routing_tests():
    """Run all user-centric routing tests."""
    logger.info("🚀 Starting user-centric routing tests...")
    
    # Run the tests
    pytest.main([
        __file__,
        "-v",
        "--tb=short",
        "--color=yes"
    ])
    
    logger.info("✅ User-centric routing tests completed!")


if __name__ == "__main__":
    run_user_centric_routing_tests()
