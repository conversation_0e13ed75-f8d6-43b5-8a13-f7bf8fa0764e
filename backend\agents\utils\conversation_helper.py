"""
Conversation Helper Utility for Agents.

This utility provides a simple interface for agents to interact with the
centralized conversation management system, eliminating the need for agents
to manage their own conversation flow.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from ..tools.mcp.conversation_manager_tool import ConversationManagerTool, ConversationAction, MessageType, ConversationMode

logger = logging.getLogger(__name__)


class ConversationHelper:
    """
    Helper class for agents to interact with the conversation management system.
    
    This class provides a simplified interface that agents can use instead of
    managing their own conversation state and flow.
    """

    def __init__(self):
        """Initialize the conversation helper."""
        self.conversation_manager = ConversationManagerTool()

    async def start_agent_conversation(
        self,
        agent_id: str,
        user_id: str,
        conversation_id: str,
        initial_message: Optional[str] = None,
        business_profile_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Start a conversation for an agent.
        
        Args:
            agent_id: ID of the agent starting the conversation
            user_id: ID of the user
            conversation_id: ID of the conversation
            initial_message: Optional initial message from the agent
            business_profile_id: Optional business profile context
            metadata: Optional metadata
            
        Returns:
            Dict containing conversation start result
        """
        try:
            # Start the conversation
            result = await self.conversation_manager.execute({
                "action": ConversationAction.START_CONVERSATION.value,
                "conversation_id": conversation_id,
                "user_id": user_id,
                "initial_agent": agent_id,
                "selected_agent": agent_id,
                "mode": ConversationMode.CONVERSATION.value,
                "business_profile_id": business_profile_id,
                "metadata": metadata or {}
            })
            
            # Add initial message if provided
            if initial_message and result.get("success"):
                await self.add_agent_message(
                    agent_id=agent_id,
                    conversation_id=conversation_id,
                    content=initial_message,
                    metadata={"message_type": "greeting"}
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error starting conversation for agent {agent_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "agent_id": agent_id
            }

    async def add_user_message(
        self,
        conversation_id: str,
        content: str,
        user_id: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        Add a user message to the conversation.
        
        Args:
            conversation_id: ID of the conversation
            content: Message content
            user_id: ID of the user (optional if already in context)
            metadata: Optional metadata
            attachments: Optional file attachments
            
        Returns:
            Dict containing message add result
        """
        return await self.conversation_manager.execute({
            "action": ConversationAction.ADD_MESSAGE.value,
            "conversation_id": conversation_id,
            "user_id": user_id or "unknown",
            "content": content,
            "sender_type": MessageType.USER.value,
            "sender_id": user_id,
            "metadata": metadata or {},
            "attachments": attachments or []
        })

    async def add_agent_message(
        self,
        agent_id: str,
        conversation_id: str,
        content: str,
        metadata: Optional[Dict[str, Any]] = None,
        in_response_to: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Add an agent message to the conversation.
        
        Args:
            agent_id: ID of the agent sending the message
            conversation_id: ID of the conversation
            content: Message content
            metadata: Optional metadata
            in_response_to: Optional ID of message this is responding to
            
        Returns:
            Dict containing message add result
        """
        return await self.conversation_manager.execute({
            "action": ConversationAction.ADD_MESSAGE.value,
            "conversation_id": conversation_id,
            "user_id": "system",  # Will be updated with actual user_id from context
            "content": content,
            "sender_type": MessageType.AGENT.value,
            "sender_id": agent_id,
            "metadata": metadata or {},
            "in_response_to": in_response_to
        })

    async def get_conversation_history(
        self,
        conversation_id: str,
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """
        Get conversation history.
        
        Args:
            conversation_id: ID of the conversation
            limit: Maximum number of messages to retrieve
            offset: Number of messages to skip
            
        Returns:
            Dict containing conversation history
        """
        return await self.conversation_manager.execute({
            "action": ConversationAction.GET_HISTORY.value,
            "conversation_id": conversation_id,
            "user_id": "system",
            "limit": limit,
            "offset": offset
        })

    async def switch_to_agent(
        self,
        conversation_id: str,
        new_agent_id: str,
        reason: str = "agent_handoff",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Switch conversation to a different agent.
        
        Args:
            conversation_id: ID of the conversation
            new_agent_id: ID of the agent to switch to
            reason: Reason for the switch
            metadata: Optional metadata
            
        Returns:
            Dict containing switch result
        """
        return await self.conversation_manager.execute({
            "action": ConversationAction.SWITCH_AGENT.value,
            "conversation_id": conversation_id,
            "user_id": "system",
            "new_agent": new_agent_id,
            "reason": reason,
            "metadata": metadata or {}
        })

    async def update_conversation_context(
        self,
        conversation_id: str,
        current_agent: Optional[str] = None,
        selected_agent: Optional[str] = None,
        mode: Optional[ConversationMode] = None,
        metadata: Optional[Dict[str, Any]] = None,
        business_profile_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update conversation context.
        
        Args:
            conversation_id: ID of the conversation
            current_agent: Current agent ID
            selected_agent: Selected agent ID
            mode: Conversation mode
            metadata: Additional metadata
            business_profile_id: Business profile ID
            
        Returns:
            Dict containing update result
        """
        update_params = {
            "action": ConversationAction.UPDATE_CONTEXT.value,
            "conversation_id": conversation_id,
            "user_id": "system"
        }
        
        if current_agent is not None:
            update_params["current_agent"] = current_agent
        if selected_agent is not None:
            update_params["selected_agent"] = selected_agent
        if mode is not None:
            update_params["mode"] = mode.value
        if metadata is not None:
            update_params["metadata"] = metadata
        if business_profile_id is not None:
            update_params["business_profile_id"] = business_profile_id
        
        return await self.conversation_manager.execute(update_params)

    async def get_conversation_state(self, conversation_id: str) -> Dict[str, Any]:
        """
        Get current conversation state.
        
        Args:
            conversation_id: ID of the conversation
            
        Returns:
            Dict containing conversation state
        """
        return await self.conversation_manager.execute({
            "action": ConversationAction.GET_STATE.value,
            "conversation_id": conversation_id,
            "user_id": "system"
        })

    async def end_conversation(self, conversation_id: str) -> Dict[str, Any]:
        """
        End a conversation.
        
        Args:
            conversation_id: ID of the conversation
            
        Returns:
            Dict containing end result
        """
        return await self.conversation_manager.execute({
            "action": ConversationAction.END_CONVERSATION.value,
            "conversation_id": conversation_id,
            "user_id": "system"
        })

    # Removed duplicate conversation handling functions
    # These are now handled by the centralized ConversationManagerTool


# Global conversation helper instance
conversation_helper = ConversationHelper()
