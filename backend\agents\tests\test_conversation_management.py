"""
Test suite for the centralized conversation management system.

This test verifies that the conversation management system works correctly
and eliminates the infinite loop issues by providing centralized control.
"""

import pytest
import asyncio
from datetime import datetime
from unittest.mock import AsyncMock, patch

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.tools.mcp.conversation_manager_tool import ConversationManagerTool, ConversationAction, MessageType
from agents.utils.conversation_helper import ConversationHelper
from agents.langgraph.agents.concierge_agent import UserSelectedConciergeAgent


class TestConversationManagement:
    """Test the centralized conversation management system."""

    @pytest.fixture
    def conversation_manager(self):
        """Create a conversation manager instance."""
        return ConversationManagerTool()

    @pytest.fixture
    def conversation_helper(self):
        """Create a conversation helper instance."""
        return ConversationHelper()

    @pytest.fixture
    def concierge_agent(self):
        """Create a concierge agent instance."""
        return UserSelectedConciergeAgent()

    @pytest.mark.asyncio
    async def test_conversation_manager_start_conversation(self, conversation_manager):
        """Test starting a conversation."""
        result = await conversation_manager.execute({
            "action": ConversationAction.START_CONVERSATION.value,
            "conversation_id": "test_conv_1",
            "user_id": "test_user_1",
            "initial_agent": "concierge-agent",
            "selected_agent": "concierge-agent"
        })
        
        assert result["success"] is True
        assert result["conversation_id"] == "test_conv_1"
        assert "context" in result

    @pytest.mark.asyncio
    async def test_conversation_manager_add_message(self, conversation_manager):
        """Test adding messages to conversation."""
        # Start conversation first
        await conversation_manager.execute({
            "action": ConversationAction.START_CONVERSATION.value,
            "conversation_id": "test_conv_2",
            "user_id": "test_user_2",
            "initial_agent": "concierge-agent"
        })
        
        # Add user message
        result = await conversation_manager.execute({
            "action": ConversationAction.ADD_MESSAGE.value,
            "conversation_id": "test_conv_2",
            "user_id": "test_user_2",
            "content": "Hello, I need help with data analysis",
            "sender_type": MessageType.USER.value,
            "sender_id": "test_user_2"
        })
        
        assert result["success"] is True
        assert "message_id" in result
        assert result["message_count"] == 1

    @pytest.mark.asyncio
    async def test_conversation_manager_get_history(self, conversation_manager):
        """Test getting conversation history."""
        conv_id = "test_conv_3"
        user_id = "test_user_3"
        
        # Start conversation and add messages
        await conversation_manager.execute({
            "action": ConversationAction.START_CONVERSATION.value,
            "conversation_id": conv_id,
            "user_id": user_id,
            "initial_agent": "concierge-agent"
        })
        
        # Add multiple messages
        for i in range(3):
            await conversation_manager.execute({
                "action": ConversationAction.ADD_MESSAGE.value,
                "conversation_id": conv_id,
                "user_id": user_id,
                "content": f"Test message {i+1}",
                "sender_type": MessageType.USER.value,
                "sender_id": user_id
            })
        
        # Get history
        result = await conversation_manager.execute({
            "action": ConversationAction.GET_HISTORY.value,
            "conversation_id": conv_id,
            "user_id": user_id,
            "limit": 10
        })
        
        assert result["success"] is True
        assert result["total_messages"] == 3
        assert len(result["messages"]) == 3

    @pytest.mark.asyncio
    async def test_conversation_helper_start_agent_conversation(self, conversation_helper):
        """Test starting conversation through helper."""
        result = await conversation_helper.start_agent_conversation(
            agent_id="concierge-agent",
            user_id="test_user_4",
            conversation_id="test_conv_4",
            initial_message="Hello! How can I help you today?"
        )
        
        assert result["success"] is True
        assert result["conversation_id"] == "test_conv_4"

    @pytest.mark.asyncio
    async def test_conversation_helper_message_flow(self, conversation_helper):
        """Test complete message flow through helper."""
        conv_id = "test_conv_5"
        user_id = "test_user_5"
        agent_id = "concierge-agent"
        
        # Start conversation
        await conversation_helper.start_agent_conversation(
            agent_id=agent_id,
            user_id=user_id,
            conversation_id=conv_id
        )
        
        # Add user message
        user_result = await conversation_helper.add_user_message(
            conversation_id=conv_id,
            content="I need help with marketing analysis",
            user_id=user_id
        )
        assert user_result["success"] is True
        
        # Add agent response
        agent_result = await conversation_helper.add_agent_message(
            agent_id=agent_id,
            conversation_id=conv_id,
            content="I can help you with marketing analysis. Let me connect you with our marketing specialist.",
            in_response_to=user_result.get("message_id")
        )
        assert agent_result["success"] is True
        
        # Get conversation history
        history = await conversation_helper.get_conversation_history(conv_id)
        assert history["success"] is True
        assert len(history["messages"]) >= 2

    @pytest.mark.asyncio
    async def test_agent_switch_functionality(self, conversation_helper):
        """Test agent switching through conversation manager."""
        conv_id = "test_conv_6"
        user_id = "test_user_6"
        
        # Start with concierge
        await conversation_helper.start_agent_conversation(
            agent_id="concierge-agent",
            user_id=user_id,
            conversation_id=conv_id
        )
        
        # Switch to marketing agent
        switch_result = await conversation_helper.switch_to_agent(
            conversation_id=conv_id,
            new_agent_id="marketing-agent",
            reason="user_requested_marketing_help"
        )
        
        assert switch_result["success"] is True
        assert switch_result["old_agent"] == "concierge-agent"
        assert switch_result["new_agent"] == "marketing-agent"

    @pytest.mark.asyncio
    async def test_concierge_agent_with_conversation_manager(self, concierge_agent):
        """Test that concierge agent works with conversation manager."""
        # Mock the unified node to avoid complex dependencies
        with patch.object(concierge_agent, 'unified_node') as mock_unified_node:
            mock_unified_node._process_message = AsyncMock(return_value={
                "messages": [{"role": "assistant", "content": "Hello! I'm here to help."}]
            })
            
            result = await concierge_agent.process_message(
                message="Hello, I need help",
                user_id="test_user_7",
                conversation_id="test_conv_7",
                context={"business_profile_id": "test_profile"}
            )
            
            assert result["success"] is True
            assert "message" in result
            assert result["metadata"]["agent_type"] == "concierge"

    @pytest.mark.asyncio
    async def test_conversation_state_consistency(self, conversation_helper):
        """Test that conversation state remains consistent."""
        conv_id = "test_conv_8"
        user_id = "test_user_8"
        agent_id = "concierge-agent"
        
        # Start conversation
        await conversation_helper.start_agent_conversation(
            agent_id=agent_id,
            user_id=user_id,
            conversation_id=conv_id
        )
        
        # Get initial state
        initial_state = await conversation_helper.get_conversation_state(conv_id)
        assert initial_state["success"] is True
        assert initial_state["context"]["current_agent"] == agent_id
        
        # Update context
        await conversation_helper.update_conversation_context(
            conversation_id=conv_id,
            metadata={"test_key": "test_value"}
        )
        
        # Get updated state
        updated_state = await conversation_helper.get_conversation_state(conv_id)
        assert updated_state["success"] is True
        assert updated_state["context"]["metadata"]["test_key"] == "test_value"

    @pytest.mark.asyncio
    async def test_infinite_loop_prevention(self, conversation_helper):
        """Test that the system prevents infinite loops."""
        conv_id = "test_conv_9"
        user_id = "test_user_9"
        agent_id = "concierge-agent"
        
        # Start conversation
        await conversation_helper.start_agent_conversation(
            agent_id=agent_id,
            user_id=user_id,
            conversation_id=conv_id
        )
        
        # Simulate rapid message additions (potential loop scenario)
        message_count = 0
        for i in range(5):
            result = await conversation_helper.add_agent_message(
                agent_id=agent_id,
                conversation_id=conv_id,
                content=f"Response {i+1}",
                metadata={"iteration": i+1}
            )
            if result["success"]:
                message_count += 1
        
        # Verify all messages were added (no loop prevention triggered at message level)
        history = await conversation_helper.get_conversation_history(conv_id)
        assert history["success"] is True
        assert len(history["messages"]) == message_count
        
        # The loop prevention should happen at the workflow level, not message level
        # This test verifies the conversation manager itself doesn't create loops

    def test_conversation_manager_tool_schema(self, conversation_manager):
        """Test that the tool schema is properly defined."""
        schema = conversation_manager.get_tool_schema()
        
        assert schema["name"] == "conversation_manager"
        assert "description" in schema
        assert "inputSchema" in schema
        assert "properties" in schema["inputSchema"]
        assert "action" in schema["inputSchema"]["properties"]
        assert "required" in schema["inputSchema"]
        assert "conversation_id" in schema["inputSchema"]["required"]
        assert "user_id" in schema["inputSchema"]["required"]


if __name__ == "__main__":
    # Run a simple test to verify the system works
    async def simple_test():
        print("Testing conversation management system...")
        
        helper = ConversationHelper()
        
        # Test basic conversation flow
        result = await helper.start_agent_conversation(
            agent_id="concierge",
            user_id="test_user",
            conversation_id="test_conv",
            initial_message="Hello! How can I help you today?"
        )
        
        print(f"Conversation start result: {result}")
        
        # Add user message
        user_msg = await helper.add_user_message(
            conversation_id="test_conv",
            content="I need help with data analysis",
            user_id="test_user"
        )
        
        print(f"User message result: {user_msg}")
        
        # Get history
        history = await helper.get_conversation_history("test_conv")
        print(f"Conversation history: {history}")
        
        print("✅ Conversation management system test completed successfully!")

    # Run the test
    asyncio.run(simple_test())
