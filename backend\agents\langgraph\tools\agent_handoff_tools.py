"""
Agent Handoff Tools for LangGraph Network Architecture.

This module provides tools that enable agents to hand off work to other agents,
delegate tasks, request consultations, and coordinate multi-agent workflows.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta
import uuid
import json

from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from ..states.network_state import (
    NetworkDatageniusState, NetworkCommunicationType, NetworkMessagePriority,
    add_network_message, create_consultation_request, create_team_formation_request
)
from ..core.agent_discovery import DiscoveryRequest, DiscoveryStrategy, discovery_service
from ..communication.messaging_system import messaging_system

logger = logging.getLogger(__name__)


class HandoffRequest(BaseModel):
    """Request for agent handoff."""
    target_agent: str = Field(description="ID of the target agent to hand off to")
    task_description: str = Field(description="Description of the task being handed off")
    context: Dict[str, Any] = Field(description="Context information for the handoff")
    priority: str = Field(default="normal", description="Priority level (critical, high, normal, low)")
    deadline: Optional[str] = Field(default=None, description="Deadline for task completion (ISO format)")
    required_capabilities: List[str] = Field(default_factory=list, description="Required capabilities for the task")


class ConsultationRequest(BaseModel):
    """Request for agent consultation."""
    specialist_agent: Optional[str] = Field(default=None, description="Specific specialist agent to consult")
    required_capability: str = Field(description="Capability needed for consultation")
    consultation_topic: str = Field(description="Topic or question for consultation")
    context: Dict[str, Any] = Field(description="Context information for consultation")
    urgency: str = Field(default="normal", description="Urgency level (high, normal, low)")


class DelegationRequest(BaseModel):
    """Request for task delegation."""
    task_name: str = Field(description="Name of the task to delegate")
    task_description: str = Field(description="Detailed description of the task")
    required_capabilities: List[str] = Field(description="Required capabilities for the task")
    preferred_agents: List[str] = Field(default_factory=list, description="Preferred agents for the task")
    exclude_agents: List[str] = Field(default_factory=list, description="Agents to exclude from consideration")
    deadline: Optional[str] = Field(default=None, description="Task deadline (ISO format)")
    priority: str = Field(default="normal", description="Task priority level")


class TeamFormationRequest(BaseModel):
    """Request for dynamic team formation."""
    task_description: str = Field(description="Description of the task requiring a team")
    required_capabilities: List[str] = Field(description="Required capabilities for the team")
    team_size: int = Field(description="Desired team size")
    duration_estimate: Optional[int] = Field(default=None, description="Estimated duration in minutes")
    coordination_style: str = Field(default="collaborative", description="Team coordination style")


class AgentHandoffTool(BaseTool):
    """Tool for handing off work to another agent."""
    
    name: str = "agent_handoff"
    description: str = """
    Hand off the current task or conversation to another agent in the network.
    Use this when another agent is better suited to handle the user's request.
    """
    args_schema = HandoffRequest

    def _run(self, **kwargs) -> str:
        """Execute agent handoff."""
        try:
            request = HandoffRequest(**kwargs)
            
            # Get current state from context
            state = self._get_current_state()
            if not state:
                return "Error: No active state found for handoff"
            
            current_agent = state.get("current_agent", "unknown")
            
            # Validate target agent exists
            from ..core.agent_registry import network_registry
            target_agent = network_registry.get_agent(request.target_agent)
            if not target_agent:
                return f"Error: Target agent '{request.target_agent}' not found in network"
            
            # Create handoff message
            priority = NetworkMessagePriority(request.priority.upper()) if request.priority.upper() in NetworkMessagePriority.__members__ else NetworkMessagePriority.NORMAL
            
            handoff_content = {
                "task_description": request.task_description,
                "context": request.context,
                "handoff_reason": f"Agent {current_agent} determined that {request.target_agent} is better suited for this task",
                "original_agent": current_agent,
                "deadline": request.deadline,
                "required_capabilities": request.required_capabilities,
                "user_context": {
                    "user_id": state.get("user_id"),
                    "conversation_id": state.get("conversation_id"),
                    "business_profile_id": state.get("business_profile_id")
                }
            }
            
            # Send handoff message
            message_id = messaging_system.send_message(
                sender_agent=current_agent,
                recipient_agent=request.target_agent,
                message_type=NetworkCommunicationType.HANDOFF,
                content=handoff_content,
                priority=priority,
                response_expected=True
            )
            
            # Update state
            state = add_network_message(
                state=state,
                sender_agent=current_agent,
                recipient_agent=request.target_agent,
                message_type=NetworkCommunicationType.HANDOFF,
                content=handoff_content,
                priority=priority
            )
            
            # Update current agent in state
            state["current_agent"] = request.target_agent
            state["handoff_history"] = state.get("handoff_history", [])
            state["handoff_history"].append({
                "from_agent": current_agent,
                "to_agent": request.target_agent,
                "timestamp": datetime.now().isoformat(),
                "reason": request.task_description,
                "message_id": message_id
            })
            
            logger.info(f"Handoff initiated from {current_agent} to {request.target_agent}")
            
            return f"Successfully handed off task to {request.target_agent}. The agent will take over from here."
            
        except Exception as e:
            logger.error(f"Error in agent handoff: {e}")
            return f"Error during handoff: {str(e)}"

    def _get_current_state(self) -> Optional[Dict[str, Any]]:
        """Get current state from execution context."""
        # This would be injected by the graph execution context
        # For now, return None - will be properly implemented in graph integration
        return None


class AgentConsultationTool(BaseTool):
    """Tool for requesting consultation from specialist agents."""
    
    name: str = "agent_consultation"
    description: str = """
    Request consultation from a specialist agent on a specific topic or capability.
    Use this when you need expert advice or specialized knowledge.
    """
    args_schema = ConsultationRequest

    async def _arun(self, **kwargs) -> str:
        """Execute agent consultation request."""
        try:
            request = ConsultationRequest(**kwargs)
            
            # Get current state
            state = self._get_current_state()
            if not state:
                return "Error: No active state found for consultation"
            
            current_agent = state.get("current_agent", "unknown")
            
            # Find specialist agent if not specified
            if not request.specialist_agent:
                specialist = await discovery_service.find_specialist_for_capability(
                    capability=request.required_capability,
                    requesting_agent=current_agent
                )
                if not specialist:
                    return f"Error: No specialist found for capability '{request.required_capability}'"
                request.specialist_agent = specialist.agent_id
            
            # Create consultation request
            state = create_consultation_request(
                state=state,
                requesting_agent=current_agent,
                specialist_agent=request.specialist_agent,
                consultation_topic=request.consultation_topic,
                context=request.context,
                urgency=request.urgency
            )
            
            logger.info(f"Consultation requested from {current_agent} to {request.specialist_agent}")
            
            return f"Consultation request sent to {request.specialist_agent}. Awaiting specialist response on '{request.consultation_topic}'."
            
        except Exception as e:
            logger.error(f"Error in agent consultation: {e}")
            return f"Error during consultation request: {str(e)}"

    def _get_current_state(self) -> Optional[Dict[str, Any]]:
        """Get current state from execution context."""
        return None


class TaskDelegationTool(BaseTool):
    """Tool for delegating tasks to other agents."""
    
    name: str = "task_delegation"
    description: str = """
    Delegate a specific task to another agent based on capabilities and availability.
    Use this when you want to assign a subtask to a more suitable agent.
    """
    args_schema = DelegationRequest

    async def _arun(self, **kwargs) -> str:
        """Execute task delegation."""
        try:
            request = DelegationRequest(**kwargs)
            
            # Get current state
            state = self._get_current_state()
            if not state:
                return "Error: No active state found for delegation"
            
            current_agent = state.get("current_agent", "unknown")
            
            # Find best agent for the task
            discovery_request = DiscoveryRequest(
                requesting_agent=current_agent,
                required_capabilities=request.required_capabilities,
                exclude_agents=request.exclude_agents + [current_agent],
                max_results=1,
                strategy=DiscoveryStrategy.HYBRID
            )
            
            results = await discovery_service.discover_agents(discovery_request)
            if not results:
                return f"Error: No suitable agent found for task '{request.task_name}'"
            
            selected_agent = results[0].agent
            
            # Create delegation
            delegation_id = str(uuid.uuid4())
            delegation_content = {
                "delegation_id": delegation_id,
                "task_name": request.task_name,
                "task_description": request.task_description,
                "required_capabilities": request.required_capabilities,
                "deadline": request.deadline,
                "priority": request.priority,
                "delegating_agent": current_agent,
                "selection_reason": results[0].recommendation_reason
            }
            
            # Send delegation message
            priority = NetworkMessagePriority(request.priority.upper()) if request.priority.upper() in NetworkMessagePriority.__members__ else NetworkMessagePriority.NORMAL
            
            message_id = await messaging_system.send_message(
                sender_agent=current_agent,
                recipient_agent=selected_agent.agent_id,
                message_type=NetworkCommunicationType.DELEGATION,
                content=delegation_content,
                priority=priority,
                response_expected=True
            )
            
            # Update state
            if "delegation_tasks" not in state:
                state["delegation_tasks"] = {}
            
            state["delegation_tasks"][delegation_id] = {
                "task_name": request.task_name,
                "assigned_agent": selected_agent.agent_id,
                "status": "delegated",
                "created_at": datetime.now().isoformat(),
                "message_id": message_id
            }
            
            logger.info(f"Task '{request.task_name}' delegated from {current_agent} to {selected_agent.agent_id}")
            
            return f"Task '{request.task_name}' successfully delegated to {selected_agent.name} ({selected_agent.agent_id}). Reason: {results[0].recommendation_reason}"
            
        except Exception as e:
            logger.error(f"Error in task delegation: {e}")
            return f"Error during task delegation: {str(e)}"

    def _get_current_state(self) -> Optional[Dict[str, Any]]:
        """Get current state from execution context."""
        return None


class TeamFormationTool(BaseTool):
    """Tool for forming dynamic teams of agents."""
    
    name: str = "team_formation"
    description: str = """
    Form a dynamic team of agents to collaborate on a complex task.
    Use this when a task requires multiple specialized agents working together.
    """
    args_schema = TeamFormationRequest

    async def _arun(self, **kwargs) -> str:
        """Execute team formation."""
        try:
            request = TeamFormationRequest(**kwargs)
            
            # Get current state
            state = self._get_current_state()
            if not state:
                return "Error: No active state found for team formation"
            
            current_agent = state.get("current_agent", "unknown")
            
            # Create team formation request
            state = create_team_formation_request(
                state=state,
                initiating_agent=current_agent,
                required_capabilities=request.required_capabilities,
                team_size=request.team_size,
                task_description=request.task_description,
                duration_estimate=request.duration_estimate
            )
            
            # Find collaboration partners
            partners = await discovery_service.find_collaboration_partners(
                requesting_agent=current_agent,
                task_capabilities=request.required_capabilities,
                team_size=request.team_size - 1  # Exclude current agent from count
            )
            
            if len(partners) < request.team_size - 1:
                return f"Warning: Only found {len(partners)} suitable agents for team of {request.team_size}"
            
            # Create team
            team_id = str(uuid.uuid4())
            team_members = [current_agent] + [agent.agent_id for agent in partners]
            
            team_data = {
                "team_id": team_id,
                "task_description": request.task_description,
                "members": team_members,
                "coordinator": current_agent,
                "required_capabilities": request.required_capabilities,
                "coordination_style": request.coordination_style,
                "status": "forming",
                "created_at": datetime.now().isoformat()
            }
            
            if "active_teams" not in state:
                state["active_teams"] = {}
            state["active_teams"][team_id] = team_data
            
            # Send collaboration invites
            for partner in partners:
                await messaging_system.send_message(
                    sender_agent=current_agent,
                    recipient_agent=partner.agent_id,
                    message_type=NetworkCommunicationType.COLLABORATION_INVITE,
                    content={
                        "team_id": team_id,
                        "task_description": request.task_description,
                        "coordinator": current_agent,
                        "team_members": team_members,
                        "required_capabilities": request.required_capabilities
                    },
                    priority=NetworkMessagePriority.HIGH,
                    response_expected=True
                )
            
            logger.info(f"Team {team_id} formed with {len(team_members)} members")
            
            member_names = [f"{agent.name} ({agent.agent_id})" for agent in partners]
            return f"Team successfully formed with {len(team_members)} members: {', '.join(member_names)}. Collaboration invites sent."
            
        except Exception as e:
            logger.error(f"Error in team formation: {e}")
            return f"Error during team formation: {str(e)}"

    def _get_current_state(self) -> Optional[Dict[str, Any]]:
        """Get current state from execution context."""
        return None


# Export all handoff tools
AGENT_HANDOFF_TOOLS = [
    AgentHandoffTool(),
    AgentConsultationTool(),
    TaskDelegationTool(),
    TeamFormationTool()
]
