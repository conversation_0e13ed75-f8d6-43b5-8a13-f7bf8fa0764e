"""
Network Workflow Manager for LangGraph Network Architecture.

This module manages multi-agent network workflows, coordinating complex
tasks that involve multiple agents working together in various patterns.
"""

import logging
import asyncio
from typing import Dict, Any, List, Optional, Set, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import uuid
import json

from ..states.network_state import NetworkDatageniusState, NetworkCommunicationType
from ..core.agent_registry import network_registry, NetworkAgent
from ..core.agent_discovery import discovery_service, DiscoveryRequest, DiscoveryStrategy
from ..communication.messaging_system import messaging_system

logger = logging.getLogger(__name__)


class WorkflowStatus(str, Enum):
    """Status of network workflows."""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class WorkflowPattern(str, Enum):
    """Common workflow patterns."""
    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    PIPELINE = "pipeline"
    CONSENSUS = "consensus"
    HIERARCHICAL = "hierarchical"
    COLLABORATIVE = "collaborative"
    COMPETITIVE = "competitive"


@dataclass
class WorkflowTask:
    """Represents a task within a workflow."""
    task_id: str
    name: str
    description: str
    assigned_agent: Optional[str] = None
    required_capabilities: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)
    status: str = "pending"
    priority: int = 1
    estimated_duration: Optional[int] = None
    actual_duration: Optional[int] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[Dict[str, Any]] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class NetworkWorkflow:
    """Represents a multi-agent network workflow."""
    workflow_id: str
    name: str
    description: str
    pattern: WorkflowPattern
    status: WorkflowStatus = WorkflowStatus.INITIALIZING
    coordinator_agent: Optional[str] = None
    participating_agents: List[str] = field(default_factory=list)
    tasks: Dict[str, WorkflowTask] = field(default_factory=dict)
    task_dependencies: Dict[str, List[str]] = field(default_factory=dict)
    workflow_data: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    deadline: Optional[datetime] = None
    progress: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)

    def get_ready_tasks(self) -> List[WorkflowTask]:
        """Get tasks that are ready to be executed."""
        ready_tasks = []
        for task in self.tasks.values():
            if task.status == "pending":
                # Check if all dependencies are completed
                dependencies_met = all(
                    self.tasks.get(dep_id, {}).status == "completed"
                    for dep_id in task.dependencies
                )
                if dependencies_met:
                    ready_tasks.append(task)
        return ready_tasks

    def get_completion_percentage(self) -> float:
        """Calculate workflow completion percentage."""
        if not self.tasks:
            return 0.0
        
        completed_tasks = sum(1 for task in self.tasks.values() if task.status == "completed")
        return (completed_tasks / len(self.tasks)) * 100.0


class NetworkWorkflowManager:
    """
    Manager for coordinating multi-agent network workflows.
    
    This manager provides:
    - Workflow creation and orchestration
    - Task assignment and coordination
    - Agent collaboration patterns
    - Progress tracking and monitoring
    - Dynamic workflow adaptation
    """

    def __init__(self):
        """Initialize the network workflow manager."""
        self.active_workflows: Dict[str, NetworkWorkflow] = {}
        self.workflow_templates: Dict[str, Dict[str, Any]] = {}
        self.task_executors: Dict[str, Callable] = {}
        self.coordination_patterns: Dict[WorkflowPattern, Callable] = {}
        
        # Performance tracking
        self.workflow_metrics: Dict[str, Dict[str, Any]] = {}
        self.agent_performance: Dict[str, Dict[str, float]] = {}
        
        # Configuration
        self.max_concurrent_workflows = 10
        self.default_task_timeout = timedelta(minutes=30)
        self.workflow_cleanup_interval = timedelta(hours=24)
        
        # Initialize coordination patterns
        self._initialize_coordination_patterns()
        
        # Background tasks
        self._monitoring_task = None
        self._cleanup_task = None
        self._start_background_tasks()
        
        logger.info("NetworkWorkflowManager initialized")

    def _initialize_coordination_patterns(self):
        """Initialize workflow coordination patterns."""
        self.coordination_patterns = {
            WorkflowPattern.SEQUENTIAL: self._coordinate_sequential_workflow,
            WorkflowPattern.PARALLEL: self._coordinate_parallel_workflow,
            WorkflowPattern.PIPELINE: self._coordinate_pipeline_workflow,
            WorkflowPattern.CONSENSUS: self._coordinate_consensus_workflow,
            WorkflowPattern.HIERARCHICAL: self._coordinate_hierarchical_workflow,
            WorkflowPattern.COLLABORATIVE: self._coordinate_collaborative_workflow,
            WorkflowPattern.COMPETITIVE: self._coordinate_competitive_workflow
        }

    def _start_background_tasks(self):
        """Start background monitoring and cleanup tasks."""
        self._monitoring_task = asyncio.create_task(self._workflow_monitoring_loop())
        self._cleanup_task = asyncio.create_task(self._workflow_cleanup_loop())

    async def _workflow_monitoring_loop(self):
        """Background loop for monitoring workflow progress."""
        while True:
            try:
                await asyncio.sleep(10)  # Check every 10 seconds
                await self._monitor_active_workflows()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in workflow monitoring loop: {e}")

    async def _workflow_cleanup_loop(self):
        """Background loop for cleaning up completed workflows."""
        while True:
            try:
                await asyncio.sleep(3600)  # Check every hour
                await self._cleanup_completed_workflows()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in workflow cleanup loop: {e}")

    async def create_workflow(
        self,
        name: str,
        description: str,
        pattern: WorkflowPattern,
        tasks: List[Dict[str, Any]],
        coordinator_agent: Optional[str] = None,
        deadline: Optional[datetime] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create a new network workflow.
        
        Args:
            name: Workflow name
            description: Workflow description
            pattern: Coordination pattern to use
            tasks: List of task definitions
            coordinator_agent: Optional coordinator agent
            deadline: Optional deadline
            metadata: Additional metadata
            
        Returns:
            Workflow ID
        """
        try:
            workflow_id = str(uuid.uuid4())
            
            # Create workflow tasks
            workflow_tasks = {}
            for task_def in tasks:
                task = WorkflowTask(
                    task_id=task_def.get("task_id", str(uuid.uuid4())),
                    name=task_def["name"],
                    description=task_def["description"],
                    required_capabilities=task_def.get("required_capabilities", []),
                    dependencies=task_def.get("dependencies", []),
                    priority=task_def.get("priority", 1),
                    estimated_duration=task_def.get("estimated_duration"),
                    metadata=task_def.get("metadata", {})
                )
                workflow_tasks[task.task_id] = task
            
            # Create workflow
            workflow = NetworkWorkflow(
                workflow_id=workflow_id,
                name=name,
                description=description,
                pattern=pattern,
                coordinator_agent=coordinator_agent,
                tasks=workflow_tasks,
                deadline=deadline,
                metadata=metadata or {}
            )
            
            # Assign agents to tasks
            await self._assign_agents_to_tasks(workflow)
            
            # Store workflow
            self.active_workflows[workflow_id] = workflow
            
            logger.info(f"Created workflow {workflow_id} with {len(tasks)} tasks")
            return workflow_id
            
        except Exception as e:
            logger.error(f"Error creating workflow: {e}")
            raise

    async def _assign_agents_to_tasks(self, workflow: NetworkWorkflow):
        """Assign agents to workflow tasks based on capabilities."""
        for task in workflow.tasks.values():
            if not task.assigned_agent and task.required_capabilities:
                # Find best agent for the task
                discovery_request = DiscoveryRequest(
                    requesting_agent=workflow.coordinator_agent or "workflow_manager",
                    required_capabilities=task.required_capabilities,
                    max_results=1,
                    strategy=DiscoveryStrategy.HYBRID
                )
                
                results = await discovery_service.discover_agents(discovery_request)
                if results:
                    task.assigned_agent = results[0].agent.agent_id
                    workflow.participating_agents.append(task.assigned_agent)
                    
                    logger.debug(f"Assigned task {task.task_id} to agent {task.assigned_agent}")

    async def start_workflow(self, workflow_id: str) -> bool:
        """
        Start executing a workflow.
        
        Args:
            workflow_id: ID of the workflow to start
            
        Returns:
            True if workflow started successfully
        """
        try:
            workflow = self.active_workflows.get(workflow_id)
            if not workflow:
                logger.error(f"Workflow {workflow_id} not found")
                return False
            
            if workflow.status != WorkflowStatus.INITIALIZING:
                logger.warning(f"Workflow {workflow_id} is not in initializing state")
                return False
            
            # Update workflow status
            workflow.status = WorkflowStatus.ACTIVE
            workflow.started_at = datetime.now()
            
            # Start coordination based on pattern
            coordination_func = self.coordination_patterns.get(workflow.pattern)
            if coordination_func:
                await coordination_func(workflow)
            else:
                logger.error(f"No coordination pattern for {workflow.pattern}")
                return False
            
            logger.info(f"Started workflow {workflow_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error starting workflow {workflow_id}: {e}")
            return False

    async def _coordinate_sequential_workflow(self, workflow: NetworkWorkflow):
        """Coordinate sequential workflow execution."""
        ready_tasks = workflow.get_ready_tasks()
        
        # Execute one task at a time
        for task in sorted(ready_tasks, key=lambda t: t.priority, reverse=True):
            if task.assigned_agent:
                await self._execute_task(workflow, task)
                break  # Only execute one task at a time in sequential mode

    async def _coordinate_parallel_workflow(self, workflow: NetworkWorkflow):
        """Coordinate parallel workflow execution."""
        ready_tasks = workflow.get_ready_tasks()
        
        # Execute all ready tasks in parallel
        execution_tasks = []
        for task in ready_tasks:
            if task.assigned_agent:
                execution_tasks.append(self._execute_task(workflow, task))
        
        if execution_tasks:
            await asyncio.gather(*execution_tasks, return_exceptions=True)

    async def _coordinate_pipeline_workflow(self, workflow: NetworkWorkflow):
        """Coordinate pipeline workflow execution."""
        # Similar to sequential but with data flow between tasks
        ready_tasks = workflow.get_ready_tasks()
        
        for task in sorted(ready_tasks, key=lambda t: t.priority, reverse=True):
            if task.assigned_agent:
                # Pass output from previous tasks as input
                task_input = self._collect_pipeline_input(workflow, task)
                await self._execute_task(workflow, task, task_input)
                break

    async def _coordinate_consensus_workflow(self, workflow: NetworkWorkflow):
        """Coordinate consensus-based workflow execution."""
        ready_tasks = workflow.get_ready_tasks()
        
        # Execute tasks that require consensus
        for task in ready_tasks:
            if task.assigned_agent and "consensus" in task.metadata:
                await self._execute_consensus_task(workflow, task)

    async def _coordinate_hierarchical_workflow(self, workflow: NetworkWorkflow):
        """Coordinate hierarchical workflow execution."""
        # Coordinator agent manages task distribution
        if workflow.coordinator_agent:
            ready_tasks = workflow.get_ready_tasks()
            
            for task in ready_tasks:
                if task.assigned_agent:
                    # Send delegation message through coordinator
                    await messaging_system.send_message(
                        sender_agent=workflow.coordinator_agent,
                        recipient_agent=task.assigned_agent,
                        message_type=NetworkCommunicationType.DELEGATION,
                        content={
                            "workflow_id": workflow.workflow_id,
                            "task_id": task.task_id,
                            "task_description": task.description,
                            "required_capabilities": task.required_capabilities
                        }
                    )

    async def _coordinate_collaborative_workflow(self, workflow: NetworkWorkflow):
        """Coordinate collaborative workflow execution."""
        ready_tasks = workflow.get_ready_tasks()
        
        # Enable agents to collaborate on tasks
        for task in ready_tasks:
            if task.assigned_agent:
                # Find collaboration partners
                partners = await discovery_service.find_collaboration_partners(
                    requesting_agent=task.assigned_agent,
                    task_capabilities=task.required_capabilities,
                    team_size=3
                )
                
                if partners:
                    # Create collaboration team
                    team_members = [task.assigned_agent] + [p.agent_id for p in partners]
                    await self._create_collaboration_team(workflow, task, team_members)

    async def _coordinate_competitive_workflow(self, workflow: NetworkWorkflow):
        """Coordinate competitive workflow execution."""
        ready_tasks = workflow.get_ready_tasks()
        
        # Assign same task to multiple agents for competition
        for task in ready_tasks:
            if task.required_capabilities:
                # Find multiple agents for the same task
                discovery_request = DiscoveryRequest(
                    requesting_agent="workflow_manager",
                    required_capabilities=task.required_capabilities,
                    max_results=3,
                    strategy=DiscoveryStrategy.PERFORMANCE_BASED
                )
                
                results = await discovery_service.discover_agents(discovery_request)
                
                # Execute task with multiple agents
                for result in results:
                    await self._execute_competitive_task(workflow, task, result.agent.agent_id)

    async def _execute_task(
        self,
        workflow: NetworkWorkflow,
        task: WorkflowTask,
        task_input: Optional[Dict[str, Any]] = None
    ):
        """Execute a workflow task."""
        try:
            if not task.assigned_agent:
                logger.error(f"No agent assigned to task {task.task_id}")
                return
            
            # Update task status
            task.status = "running"
            task.started_at = datetime.now()
            
            # Send task execution message
            await messaging_system.send_message(
                sender_agent="workflow_manager",
                recipient_agent=task.assigned_agent,
                message_type=NetworkCommunicationType.DELEGATION,
                content={
                    "workflow_id": workflow.workflow_id,
                    "task_id": task.task_id,
                    "task_name": task.name,
                    "task_description": task.description,
                    "task_input": task_input or {},
                    "required_capabilities": task.required_capabilities,
                    "deadline": task.metadata.get("deadline")
                },
                response_expected=True
            )
            
            logger.debug(f"Executing task {task.task_id} with agent {task.assigned_agent}")
            
        except Exception as e:
            logger.error(f"Error executing task {task.task_id}: {e}")
            task.status = "failed"

    def _collect_pipeline_input(self, workflow: NetworkWorkflow, task: WorkflowTask) -> Dict[str, Any]:
        """Collect input data from completed dependency tasks."""
        pipeline_input = {}
        
        for dep_task_id in task.dependencies:
            dep_task = workflow.tasks.get(dep_task_id)
            if dep_task and dep_task.status == "completed" and dep_task.result:
                pipeline_input[dep_task_id] = dep_task.result
        
        return pipeline_input

    async def _execute_consensus_task(self, workflow: NetworkWorkflow, task: WorkflowTask):
        """Execute a task that requires consensus."""
        # Implementation for consensus-based task execution
        pass

    async def _create_collaboration_team(
        self,
        workflow: NetworkWorkflow,
        task: WorkflowTask,
        team_members: List[str]
    ):
        """Create a collaboration team for a task."""
        # Implementation for creating collaboration teams
        pass

    async def _execute_competitive_task(
        self,
        workflow: NetworkWorkflow,
        task: WorkflowTask,
        agent_id: str
    ):
        """Execute a task in competitive mode."""
        # Implementation for competitive task execution
        pass

    async def _monitor_active_workflows(self):
        """Monitor progress of active workflows."""
        for workflow in self.active_workflows.values():
            if workflow.status == WorkflowStatus.ACTIVE:
                # Update progress
                workflow.progress = workflow.get_completion_percentage()
                
                # Check if workflow is completed
                if workflow.progress >= 100.0:
                    workflow.status = WorkflowStatus.COMPLETED
                    workflow.completed_at = datetime.now()
                    logger.info(f"Workflow {workflow.workflow_id} completed")
                
                # Check for deadline violations
                if workflow.deadline and datetime.now() > workflow.deadline:
                    workflow.status = WorkflowStatus.FAILED
                    logger.warning(f"Workflow {workflow.workflow_id} missed deadline")

    async def _cleanup_completed_workflows(self):
        """Clean up old completed workflows."""
        cutoff_time = datetime.now() - self.workflow_cleanup_interval
        workflows_to_remove = []
        
        for workflow_id, workflow in self.active_workflows.items():
            if (workflow.status in [WorkflowStatus.COMPLETED, WorkflowStatus.FAILED, WorkflowStatus.CANCELLED] and
                workflow.completed_at and workflow.completed_at < cutoff_time):
                workflows_to_remove.append(workflow_id)
        
        for workflow_id in workflows_to_remove:
            del self.active_workflows[workflow_id]
            logger.info(f"Cleaned up workflow {workflow_id}")

    def get_workflow_status(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """Get status of a workflow."""
        workflow = self.active_workflows.get(workflow_id)
        if not workflow:
            return None
        
        return {
            "workflow_id": workflow.workflow_id,
            "name": workflow.name,
            "status": workflow.status.value,
            "progress": workflow.progress,
            "participating_agents": workflow.participating_agents,
            "tasks": {
                task_id: {
                    "name": task.name,
                    "status": task.status,
                    "assigned_agent": task.assigned_agent
                }
                for task_id, task in workflow.tasks.items()
            },
            "created_at": workflow.created_at.isoformat(),
            "started_at": workflow.started_at.isoformat() if workflow.started_at else None,
            "completed_at": workflow.completed_at.isoformat() if workflow.completed_at else None
        }

    def get_manager_stats(self) -> Dict[str, Any]:
        """Get workflow manager statistics."""
        active_count = len([w for w in self.active_workflows.values() if w.status == WorkflowStatus.ACTIVE])
        completed_count = len([w for w in self.active_workflows.values() if w.status == WorkflowStatus.COMPLETED])
        
        return {
            "total_workflows": len(self.active_workflows),
            "active_workflows": active_count,
            "completed_workflows": completed_count,
            "workflow_patterns": list(self.coordination_patterns.keys()),
            "average_completion_time": self._calculate_average_completion_time()
        }

    def _calculate_average_completion_time(self) -> Optional[float]:
        """Calculate average workflow completion time."""
        completed_workflows = [
            w for w in self.active_workflows.values() 
            if w.status == WorkflowStatus.COMPLETED and w.started_at and w.completed_at
        ]
        
        if not completed_workflows:
            return None
        
        total_duration = sum(
            (w.completed_at - w.started_at).total_seconds()
            for w in completed_workflows
        )
        
        return total_duration / len(completed_workflows)

    async def shutdown(self):
        """Shutdown the workflow manager."""
        if self._monitoring_task:
            self._monitoring_task.cancel()
        if self._cleanup_task:
            self._cleanup_task.cancel()
        
        logger.info("NetworkWorkflowManager shutdown")


# Global workflow manager instance
network_workflow_manager = NetworkWorkflowManager()
