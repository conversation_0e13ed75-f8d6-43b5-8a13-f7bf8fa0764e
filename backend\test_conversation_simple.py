"""
Simple test for the conversation management system.
"""

import asyncio
import sys
import os

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from agents.tools.mcp.conversation_manager_tool import ConversationManagerTool, ConversationAction, MessageType
from agents.utils.conversation_helper import ConversationHelper


async def test_conversation_system():
    """Test the conversation management system."""
    print("🧪 Testing Conversation Management System...")
    
    try:
        # Test 1: Create conversation manager
        print("\n1. Creating conversation manager...")
        manager = ConversationManagerTool()
        print("✅ Conversation manager created successfully")
        
        # Test 2: Start a conversation
        print("\n2. Starting a conversation...")
        result = await manager.execute({
            "action": ConversationAction.START_CONVERSATION.value,
            "conversation_id": "test_conv_1",
            "user_id": "test_user_1",
            "initial_agent": "concierge-agent",
            "selected_agent": "concierge-agent"
        })
        
        if result["success"]:
            print("✅ Conversation started successfully")
            print(f"   Conversation ID: {result['conversation_id']}")
        else:
            print(f"❌ Failed to start conversation: {result.get('error')}")
            return False
        
        # Test 3: Add a user message
        print("\n3. Adding user message...")
        msg_result = await manager.execute({
            "action": ConversationAction.ADD_MESSAGE.value,
            "conversation_id": "test_conv_1",
            "user_id": "test_user_1",
            "content": "Hello, I need help with data analysis",
            "sender_type": MessageType.USER.value,
            "sender_id": "test_user_1"
        })
        
        if msg_result["success"]:
            print("✅ User message added successfully")
            print(f"   Message ID: {msg_result['message_id']}")
        else:
            print(f"❌ Failed to add user message: {msg_result.get('error')}")
            return False
        
        # Test 4: Add an agent response
        print("\n4. Adding agent response...")
        agent_result = await manager.execute({
            "action": ConversationAction.ADD_MESSAGE.value,
            "conversation_id": "test_conv_1",
            "user_id": "test_user_1",
            "content": "I can help you with data analysis. Let me connect you with our analysis specialist.",
            "sender_type": MessageType.AGENT.value,
            "sender_id": "concierge-agent",
            "in_response_to": msg_result["message_id"]
        })
        
        if agent_result["success"]:
            print("✅ Agent response added successfully")
            print(f"   Response ID: {agent_result['message_id']}")
        else:
            print(f"❌ Failed to add agent response: {agent_result.get('error')}")
            return False
        
        # Test 5: Get conversation history
        print("\n5. Getting conversation history...")
        history_result = await manager.execute({
            "action": ConversationAction.GET_HISTORY.value,
            "conversation_id": "test_conv_1",
            "user_id": "test_user_1",
            "limit": 10
        })
        
        if history_result["success"]:
            print("✅ Conversation history retrieved successfully")
            print(f"   Total messages: {history_result['total_messages']}")
            for i, msg in enumerate(history_result["messages"]):
                print(f"   Message {i+1}: {msg['sender_type']} - {msg['content'][:50]}...")
        else:
            print(f"❌ Failed to get conversation history: {history_result.get('error')}")
            return False
        
        # Test 6: Test conversation helper
        print("\n6. Testing conversation helper...")
        helper = ConversationHelper()
        
        helper_result = await helper.start_agent_conversation(
            agent_id="concierge-agent",
            user_id="test_user_2",
            conversation_id="test_conv_2",
            initial_message="Hello! How can I help you today?"
        )
        
        if helper_result["success"]:
            print("✅ Conversation helper works successfully")
            print(f"   Helper conversation ID: {helper_result['conversation_id']}")
        else:
            print(f"❌ Conversation helper failed: {helper_result.get('error')}")
            return False
        
        # Test 7: Test agent switching
        print("\n7. Testing agent switching...")
        switch_result = await helper.switch_to_agent(
            conversation_id="test_conv_2",
            new_agent_id="analysis-agent",
            reason="user_requested_analysis"
        )
        
        if switch_result["success"]:
            print("✅ Agent switching works successfully")
            print(f"   Switched from {switch_result['old_agent']} to {switch_result['new_agent']}")
        else:
            print(f"❌ Agent switching failed: {switch_result.get('error')}")
            return False
        
        # Test 8: Test conversation state
        print("\n8. Testing conversation state...")
        state_result = await helper.get_conversation_state("test_conv_2")
        
        if state_result["success"]:
            print("✅ Conversation state retrieved successfully")
            context = state_result["context"]
            print(f"   Current agent: {context['current_agent']}")
            print(f"   Selected agent: {context['selected_agent']}")
            print(f"   Mode: {context['mode']}")
        else:
            print(f"❌ Failed to get conversation state: {state_result.get('error')}")
            return False
        
        print("\n🎉 All tests passed! Conversation management system is working correctly.")
        print("\n📋 Summary:")
        print("   ✅ Conversation creation and management")
        print("   ✅ Message handling (user and agent)")
        print("   ✅ Conversation history tracking")
        print("   ✅ Agent switching functionality")
        print("   ✅ State management and consistency")
        print("   ✅ Helper utility functions")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_infinite_loop_prevention():
    """Test that the system prevents infinite loops."""
    print("\n🔄 Testing infinite loop prevention...")
    
    try:
        helper = ConversationHelper()
        
        # Start conversation
        await helper.start_agent_conversation(
            agent_id="concierge-agent",
            user_id="test_user_loop",
            conversation_id="test_conv_loop"
        )
        
        # Simulate rapid agent responses (potential loop scenario)
        print("   Simulating rapid agent responses...")
        success_count = 0
        
        for i in range(10):
            result = await helper.add_agent_message(
                agent_id="concierge-agent",
                conversation_id="test_conv_loop",
                content=f"Automated response {i+1}",
                metadata={"iteration": i+1, "test": "loop_prevention"}
            )
            
            if result["success"]:
                success_count += 1
            
            # Small delay to simulate processing time
            await asyncio.sleep(0.01)
        
        print(f"   ✅ Successfully added {success_count}/10 messages without loops")
        print("   ✅ No infinite loops detected at conversation level")
        
        # Get final state
        final_state = await helper.get_conversation_state("test_conv_loop")
        if final_state["success"]:
            print(f"   ✅ Final message count: {final_state['message_count']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Loop prevention test failed: {e}")
        return False


async def main():
    """Run all tests."""
    print("🚀 Starting Conversation Management System Tests")
    print("=" * 60)
    
    # Run main functionality tests
    main_test_passed = await test_conversation_system()
    
    # Run loop prevention tests
    loop_test_passed = await test_infinite_loop_prevention()
    
    print("\n" + "=" * 60)
    if main_test_passed and loop_test_passed:
        print("🎉 ALL TESTS PASSED! The conversation management system is ready.")
        print("\n💡 Key Benefits:")
        print("   • Centralized conversation control")
        print("   • No more agent-specific conversation logic")
        print("   • Consistent message handling across all agents")
        print("   • Built-in loop prevention mechanisms")
        print("   • Dynamic agent switching capabilities")
        print("   • Unified conversation state management")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)
